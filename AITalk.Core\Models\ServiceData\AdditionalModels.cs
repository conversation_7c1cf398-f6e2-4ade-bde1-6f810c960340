using System;
using System.Collections.Generic;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 定价信息
    /// </summary>
    public class PricingInfo
    {
        public string PricingModel { get; set; } = "PayPerUse"; // PayPerUse, Subscription, Hybrid
        public decimal InputTokenPrice { get; set; } = 0.0m;
        public decimal OutputTokenPrice { get; set; } = 0.0m;
        public decimal RequestPrice { get; set; } = 0.0m;
        public decimal MonthlySubscriptionFee { get; set; } = 0.0m;
        public string Currency { get; set; } = "USD";
        public bool HasFreeTier { get; set; } = false;
        public long FreeTierTokenLimit { get; set; } = 0;
        public long FreeTierRequestLimit { get; set; } = 0;
        public Dictionary<string, decimal> ModelSpecificPricing { get; set; } = new();
        public Dictionary<string, decimal> VolumeDiscounts { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 服务等级协议
    /// </summary>
    public class ServiceLevelAgreement
    {
        public double UptimeGuarantee { get; set; } = 0.99; // 99%
        public TimeSpan MaxResponseTime { get; set; } = TimeSpan.FromSeconds(5);
        public double MaxErrorRate { get; set; } = 0.01; // 1%
        public string SupportLevel { get; set; } = "Standard";
        public TimeSpan SupportResponseTime { get; set; } = TimeSpan.FromHours(24);
        public List<string> SupportChannels { get; set; } = new();
        public string DataRetentionPolicy { get; set; } = string.Empty;
        public string BackupPolicy { get; set; } = string.Empty;
        public string DisasterRecoveryPlan { get; set; } = string.Empty;
        public List<string> ComplianceStandards { get; set; } = new();
        public DateTime EffectiveDate { get; set; } = DateTime.UtcNow;
        public DateTime? ExpiryDate { get; set; }
    }

    /// <summary>
    /// 监控配置
    /// </summary>
    public class MonitoringConfiguration
    {
        public bool EnableHealthChecks { get; set; } = true;
        public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(1);
        public bool EnablePerformanceMonitoring { get; set; } = true;
        public TimeSpan MetricsCollectionInterval { get; set; } = TimeSpan.FromSeconds(30);
        public bool EnableAlerting { get; set; } = true;
        public List<AlertRule> AlertRules { get; set; } = new();
        public bool EnableLogging { get; set; } = true;
        public string LogLevel { get; set; } = "Information";
        public bool EnableTracing { get; set; } = false;
        public string TracingEndpoint { get; set; } = string.Empty;
        public Dictionary<string, object> CustomMetrics { get; set; } = new();
    }

    /// <summary>
    /// 告警规则
    /// </summary>
    public class AlertRule
    {
        public string Name { get; set; } = string.Empty;
        public string Condition { get; set; } = string.Empty;
        public string Threshold { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; } = TimeSpan.FromMinutes(5);
        public string Severity { get; set; } = "Warning";
        public List<string> NotificationChannels { get; set; } = new();
        public bool IsEnabled { get; set; } = true;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 备份配置
    /// </summary>
    public class BackupConfiguration
    {
        public bool EnableBackup { get; set; } = true;
        public TimeSpan BackupInterval { get; set; } = TimeSpan.FromHours(24);
        public int RetentionDays { get; set; } = 30;
        public string BackupLocation { get; set; } = string.Empty;
        public bool EnableEncryption { get; set; } = true;
        public string EncryptionKey { get; set; } = string.Empty;
        public bool EnableCompression { get; set; } = true;
        public List<string> BackupTypes { get; set; } = new() { "Configuration", "Data", "Logs" };
        public bool EnableAutomaticRestore { get; set; } = false;
        public TimeSpan MaxRestoreTime { get; set; } = TimeSpan.FromHours(4);
        public Dictionary<string, object> BackupMetadata { get; set; } = new();
    }
}
