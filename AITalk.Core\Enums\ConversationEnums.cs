using System;

namespace AITalk.Core.Enums
{
    /// <summary>
    /// 对话类型枚举
    /// </summary>
    public enum ConversationType
    {
        SingleRound,
        MultiRound,
        TaskOriented,
        Casual,
        Streaming,
        Batch
    }

    /// <summary>
    /// 对话优先级枚举
    /// </summary>
    public enum ConversationPriority
    {
        Low,
        Normal,
        High,
        Urgent,
        Critical
    }

    /// <summary>
    /// 对话状态枚举
    /// </summary>
    public enum ConversationStatus
    {
        Created,
        InProgress,
        Paused,
        Completed,
        Failed,
        Cancelled,
        Timeout,
        Archived
    }

    /// <summary>
    /// 消息角色枚举
    /// </summary>
    public enum MessageRole
    {
        User,
        Assistant,
        System,
        Function,
        Tool
    }

    /// <summary>
    /// 消息内容类型枚举
    /// </summary>
    public enum MessageContentType
    {
        Text,
        Image,
        Audio,
        Video,
        File,
        Json,
        Markdown,
        Html
    }

    /// <summary>
    /// 任务类型枚举
    /// </summary>
    public enum TaskType
    {
        InitialAnalysis,
        InformationGathering,
        ContextBuilding,
        PrimaryProcessing,
        SecondaryProcessing,
        ResultValidation,
        ResultRefinement,
        QualityCheck,
        UserConfirmation,
        FinalSynthesis,
        ErrorHandling,
        Retry,
        Rollback,
        Cleanup,
        ChatCompletion,
        FunctionCall,
        DataRetrieval,
        Analysis,
        Custom
    }

    /// <summary>
    /// 任务状态枚举
    /// </summary>
    public enum TaskStatus
    {
        Pending,
        Running,
        Completed,
        Failed,
        Cancelled,
        Timeout,
        Skipped,
        Retrying
    }

    /// <summary>
    /// 任务执行状态枚举
    /// </summary>
    public enum TaskExecutionStatus
    {
        NotStarted,
        Initializing,
        Running,
        Waiting,
        Suspended,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// 循环检测结果枚举
    /// </summary>
    public enum LoopDetectionResult
    {
        NoLoop,
        PotentialLoop,
        ConfirmedLoop,
        InfiniteLoop
    }

    /// <summary>
    /// 循环中断策略枚举
    /// </summary>
    public enum LoopBreakingStrategy
    {
        ModifyTaskParameters,
        ChangeExecutionOrder,
        IntroduceRandomness,
        RequestUserIntervention,
        UseAlternativeService,
        SimplifyTask,
        SkipOptionalSteps,
        ForceTermination
    }

    /// <summary>
    /// 下一步动作枚举
    /// </summary>
    public enum NextAction
    {
        Continue,
        Pause,
        Complete,
        Retry,
        Escalate,
        RequestInput,
        Terminate
    }

    /// <summary>
    /// 结果冲突类型枚举
    /// </summary>
    public enum ResultConflictType
    {
        Contradiction,
        Inconsistency,
        Ambiguity,
        Incompleteness,
        Redundancy
    }

    /// <summary>
    /// 冲突解决策略枚举
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        UseLatest,
        UseHighestConfidence,
        Merge,
        RequestClarification,
        UseDefault,
        Manual
    }


}
