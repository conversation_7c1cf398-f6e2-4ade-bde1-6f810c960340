using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AITalk.Core.Enums;
using AITalk.Core.Models;
using AITalk.Core.Models.OpenAI;
using AITalk.Core.Services.Interfaces;

namespace AITalk.Core.Services
{
    /// <summary>
    /// OpenAI格式API管理器 - 管理第三方OpenAI兼容API
    /// </summary>
    public class OpenAIApiManager : IOpenAIApiManager
    {
        private readonly Dictionary<ProviderType, IOpenAICompatibleProvider> _providers;
        private readonly Dictionary<Guid, ApiConnection> _activeConnections;
        private readonly IOpenAIRequestRouter _requestRouter;
        private readonly IOpenAIResponseNormalizer _responseNormalizer;
        private readonly IProviderCompatibilityChecker _compatibilityChecker;
        private readonly IEndpointMapper _endpointMapper;
        private readonly IOpenAIModelManager _modelManager;
        private readonly IStreamingResponseHandler _streamingHandler;
        private readonly IOpenAIErrorHandler _errorHandler;
        private readonly IRateLimitManager _rateLimitManager;

        public OpenAIApiManager(
            IOpenAIRequestRouter requestRouter,
            IOpenAIResponseNormalizer responseNormalizer,
            IProviderCompatibilityChecker compatibilityChecker,
            IEndpointMapper endpointMapper,
            IOpenAIModelManager modelManager,
            IStreamingResponseHandler streamingHandler,
            IOpenAIErrorHandler errorHandler,
            IRateLimitManager rateLimitManager)
        {
            _providers = new Dictionary<ProviderType, IOpenAICompatibleProvider>();
            _activeConnections = new Dictionary<Guid, ApiConnection>();
            _requestRouter = requestRouter ?? throw new ArgumentNullException(nameof(requestRouter));
            _responseNormalizer = responseNormalizer ?? throw new ArgumentNullException(nameof(responseNormalizer));
            _compatibilityChecker = compatibilityChecker ?? throw new ArgumentNullException(nameof(compatibilityChecker));
            _endpointMapper = endpointMapper ?? throw new ArgumentNullException(nameof(endpointMapper));
            _modelManager = modelManager ?? throw new ArgumentNullException(nameof(modelManager));
            _streamingHandler = streamingHandler ?? throw new ArgumentNullException(nameof(streamingHandler));
            _errorHandler = errorHandler ?? throw new ArgumentNullException(nameof(errorHandler));
            _rateLimitManager = rateLimitManager ?? throw new ArgumentNullException(nameof(rateLimitManager));

            InitializeDefaultProviders();
        }

        #region 提供商管理

        /// <summary>
        /// 注册OpenAI兼容提供商
        /// </summary>
        public async Task<bool> RegisterProviderAsync(OpenAICompatibleProvider provider)
        {
            try
            {
                // 验证提供商配置
                var validationResult = await ValidateProviderConfigurationAsync(provider);
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"Provider configuration is invalid: {string.Join(", ", validationResult.Errors)}");
                }

                // 检查兼容性
                var compatibilityReport = await _compatibilityChecker.CheckOpenAICompatibilityAsync(provider);
                if (compatibilityReport.CompatibilityLevel == CompatibilityLevel.None)
                {
                    throw new NotSupportedException($"Provider {provider.ProviderName} is not compatible with OpenAI API");
                }

                // 测试连接
                var connectionTest = await _compatibilityChecker.TestProviderConnectionAsync(provider);
                if (!connectionTest.IsSuccessful)
                {
                    throw new InvalidOperationException($"Failed to connect to provider: {connectionTest.ErrorMessage}");
                }

                // 注册提供商
                var providerInstance = CreateProviderInstance(provider);
                _providers[provider.ProviderType] = providerInstance;

                return true;
            }
            catch (Exception ex)
            {
                // 记录错误
                await LogErrorAsync($"Failed to register provider {provider.ProviderName}", ex);
                return false;
            }
        }

        /// <summary>
        /// 注销提供商
        /// </summary>
        public async Task<bool> UnregisterProviderAsync(ProviderType providerType)
        {
            try
            {
                if (_providers.ContainsKey(providerType))
                {
                    var provider = _providers[providerType];
                    await provider.DisconnectAsync();
                    _providers.Remove(providerType);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"Failed to unregister provider {providerType}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取可用提供商
        /// </summary>
        public async Task<List<ProviderType>> GetAvailableProvidersAsync()
        {
            var availableProviders = new List<ProviderType>();
            
            foreach (var kvp in _providers)
            {
                try
                {
                    var healthStatus = await kvp.Value.GetHealthStatusAsync();
                    if (healthStatus == ProviderHealthStatus.Healthy)
                    {
                        availableProviders.Add(kvp.Key);
                    }
                }
                catch
                {
                    // 忽略健康检查失败的提供商
                }
            }

            return availableProviders;
        }

        #endregion

        #region API调用

        /// <summary>
        /// 发送聊天完成请求
        /// </summary>
        public async Task<ChatCompletionResponse> SendChatCompletionAsync(
            ChatCompletionRequest request, 
            ProviderType? preferredProvider = null)
        {
            try
            {
                // 选择提供商
                var provider = await SelectProviderAsync(request, preferredProvider);
                if (provider == null)
                {
                    throw new InvalidOperationException("No available provider found for the request");
                }

                // 检查速率限制
                var rateLimitStatus = await _rateLimitManager.CheckRateLimitAsync(provider.ProviderType, "chat/completions");
                if (!rateLimitStatus.CanProceed)
                {
                    throw new RateLimitExceededException($"Rate limit exceeded for provider {provider.ProviderType}");
                }

                // 路由和转换请求
                var routedRequest = await _requestRouter.RouteRequestAsync(request, provider.ProviderType);
                
                // 发送请求
                var response = await provider.SendChatCompletionAsync(routedRequest);
                
                // 标准化响应
                var normalizedResponse = await _responseNormalizer.NormalizeChatCompletionResponseAsync(response, provider.ProviderType);
                
                // 记录速率限制信息
                await _rateLimitManager.TrackRequestAsync(provider.ProviderType, "chat/completions");
                
                return normalizedResponse;
            }
            catch (Exception ex)
            {
                var openAIError = await _errorHandler.HandleApiErrorAsync(ex, preferredProvider ?? ProviderType.OpenAI);
                throw new OpenAIApiException(openAIError.ErrorMessage, openAIError.ErrorCode, ex);
            }
        }

        /// <summary>
        /// 发送流式聊天完成请求
        /// </summary>
        public async IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(
            ChatCompletionRequest request, 
            ProviderType? preferredProvider = null)
        {
            IOpenAICompatibleProvider? provider = null;
            
            try
            {
                // 选择提供商
                provider = await SelectProviderAsync(request, preferredProvider);
                if (provider == null)
                {
                    throw new InvalidOperationException("No available provider found for the request");
                }

                // 检查流式支持
                if (!provider.SupportsStreaming)
                {
                    throw new NotSupportedException($"Provider {provider.ProviderType} does not support streaming");
                }

                // 设置流式请求
                request.Stream = true;
                
                // 路由和转换请求
                var routedRequest = await _requestRouter.RouteRequestAsync(request, provider.ProviderType);
                
                // 发送流式请求
                await foreach (var chunk in provider.SendStreamingChatCompletionAsync(routedRequest))
                {
                    // 标准化流式响应块
                    var normalizedChunk = await _streamingHandler.NormalizeStreamingChunkAsync(chunk, provider.ProviderType);
                    yield return normalizedChunk;
                }
            }
            catch (Exception ex)
            {
                var openAIError = await _errorHandler.HandleApiErrorAsync(ex, provider?.ProviderType ?? ProviderType.OpenAI);
                throw new OpenAIApiException(openAIError.ErrorMessage, openAIError.ErrorCode, ex);
            }
        }

        /// <summary>
        /// 获取可用模型
        /// </summary>
        public async Task<List<ModelInfo>> GetAvailableModelsAsync(ProviderType? providerType = null)
        {
            var allModels = new List<ModelInfo>();

            if (providerType.HasValue)
            {
                if (_providers.TryGetValue(providerType.Value, out var provider))
                {
                    var models = await _modelManager.GetAvailableModelsAsync(providerType.Value);
                    allModels.AddRange(models);
                }
            }
            else
            {
                // 获取所有提供商的模型
                foreach (var kvp in _providers)
                {
                    try
                    {
                        var models = await _modelManager.GetAvailableModelsAsync(kvp.Key);
                        allModels.AddRange(models);
                    }
                    catch
                    {
                        // 忽略获取失败的提供商
                    }
                }
            }

            return allModels;
        }

        /// <summary>
        /// 获取模型信息
        /// </summary>
        public async Task<ModelInfo?> GetModelInfoAsync(string modelName, ProviderType? providerType = null)
        {
            if (providerType.HasValue)
            {
                return await _modelManager.GetModelInfoAsync(modelName, providerType.Value);
            }

            // 在所有提供商中查找模型
            foreach (var kvp in _providers)
            {
                try
                {
                    var modelInfo = await _modelManager.GetModelInfoAsync(modelName, kvp.Key);
                    if (modelInfo != null)
                    {
                        return modelInfo;
                    }
                }
                catch
                {
                    // 继续查找其他提供商
                }
            }

            return null;
        }

        #endregion

        #region 提供商选择和路由

        /// <summary>
        /// 选择最佳提供商
        /// </summary>
        private async Task<IOpenAICompatibleProvider?> SelectProviderAsync(
            ChatCompletionRequest request, 
            ProviderType? preferredProvider = null)
        {
            // 如果指定了首选提供商，优先使用
            if (preferredProvider.HasValue && _providers.TryGetValue(preferredProvider.Value, out var preferred))
            {
                var health = await preferred.GetHealthStatusAsync();
                if (health == ProviderHealthStatus.Healthy)
                {
                    return preferred;
                }
            }

            // 根据模型名称选择提供商
            var modelProvider = await SelectProviderByModelAsync(request.Model);
            if (modelProvider != null)
            {
                return modelProvider;
            }

            // 选择最佳可用提供商
            return await SelectBestAvailableProviderAsync();
        }

        /// <summary>
        /// 根据模型选择提供商
        /// </summary>
        private async Task<IOpenAICompatibleProvider?> SelectProviderByModelAsync(string modelName)
        {
            foreach (var kvp in _providers)
            {
                try
                {
                    var modelInfo = await _modelManager.GetModelInfoAsync(modelName, kvp.Key);
                    if (modelInfo != null)
                    {
                        var health = await kvp.Value.GetHealthStatusAsync();
                        if (health == ProviderHealthStatus.Healthy)
                        {
                            return kvp.Value;
                        }
                    }
                }
                catch
                {
                    // 继续检查其他提供商
                }
            }

            return null;
        }

        /// <summary>
        /// 选择最佳可用提供商
        /// </summary>
        private async Task<IOpenAICompatibleProvider?> SelectBestAvailableProviderAsync()
        {
            IOpenAICompatibleProvider? bestProvider = null;
            double bestScore = 0;

            foreach (var kvp in _providers)
            {
                try
                {
                    var health = await kvp.Value.GetHealthStatusAsync();
                    if (health == ProviderHealthStatus.Healthy)
                    {
                        var score = await CalculateProviderScoreAsync(kvp.Value);
                        if (score > bestScore)
                        {
                            bestScore = score;
                            bestProvider = kvp.Value;
                        }
                    }
                }
                catch
                {
                    // 忽略评分失败的提供商
                }
            }

            return bestProvider;
        }

        /// <summary>
        /// 计算提供商评分
        /// </summary>
        private async Task<double> CalculateProviderScoreAsync(IOpenAICompatibleProvider provider)
        {
            try
            {
                var metrics = await provider.GetPerformanceMetricsAsync();
                
                // 基于响应时间、成功率等计算评分
                var responseTimeScore = Math.Max(0, 100 - metrics.AverageResponseTime.TotalMilliseconds / 10);
                var successRateScore = metrics.SuccessRate * 100;
                var availabilityScore = metrics.AvailabilityPercentage;
                
                return (responseTimeScore + successRateScore + availabilityScore) / 3;
            }
            catch
            {
                return 0;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化默认提供商
        /// </summary>
        private void InitializeDefaultProviders()
        {
            // 这里可以初始化一些默认的提供商配置
            // 实际实现中可以从配置文件加载
        }

        /// <summary>
        /// 创建提供商实例
        /// </summary>
        private IOpenAICompatibleProvider CreateProviderInstance(OpenAICompatibleProvider providerConfig)
        {
            return providerConfig.ProviderType switch
            {
                ProviderType.OpenAI => new OpenAIProvider(providerConfig),
                ProviderType.Azure => new AzureOpenAIProvider(providerConfig),
                ProviderType.Anthropic => new AnthropicProvider(providerConfig),
                ProviderType.Google => new GoogleProvider(providerConfig),
                ProviderType.Ollama => new OllamaProvider(providerConfig),
                _ => new GenericOpenAIProvider(providerConfig)
            };
        }

        /// <summary>
        /// 验证提供商配置
        /// </summary>
        private async Task<ValidationResult> ValidateProviderConfigurationAsync(OpenAICompatibleProvider provider)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrEmpty(provider.BaseUrl))
            {
                result.IsValid = false;
                result.Errors.Add("BaseUrl is required");
            }

            if (string.IsNullOrEmpty(provider.ProviderName))
            {
                result.IsValid = false;
                result.Errors.Add("ProviderName is required");
            }

            // 验证URL格式
            if (!string.IsNullOrEmpty(provider.BaseUrl) && !Uri.TryCreate(provider.BaseUrl, UriKind.Absolute, out _))
            {
                result.IsValid = false;
                result.Errors.Add("BaseUrl is not a valid URL");
            }

            return await Task.FromResult(result);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        private async Task LogErrorAsync(string message, Exception exception)
        {
            // 这里可以集成日志系统
            Console.WriteLine($"[ERROR] {message}: {exception.Message}");
            await Task.CompletedTask;
        }

        #endregion

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            foreach (var provider in _providers.Values)
            {
                provider?.Dispose();
            }
            _providers.Clear();
            _activeConnections.Clear();
        }
    }
}
