using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ConversationData
{
    /// <summary>
    /// AI任务 - 多轮调度的核心单元
    /// </summary>
    public class AITask
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public Guid TaskId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 任务类型
        /// </summary>
        public TaskType TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 任务描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 状态
        /// </summary>
        public AITalk.Core.Enums.TaskStatus Status { get; set; } = AITalk.Core.Enums.TaskStatus.Pending;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 预估持续时间
        /// </summary>
        public TimeSpan? EstimatedDuration { get; set; }

        /// <summary>
        /// 实际持续时间
        /// </summary>
        public TimeSpan? ActualDuration => CompletedAt?.Subtract(StartedAt ?? DateTime.UtcNow);

        /// <summary>
        /// 进度百分比 (0-100)
        /// </summary>
        public double Progress { get; set; } = 0.0;

        /// <summary>
        /// 输入数据
        /// </summary>
        public object? Input { get; set; }

        /// <summary>
        /// 输出数据
        /// </summary>
        public object? Output { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public Exception? Error { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// 依赖任务ID列表
        /// </summary>
        public List<Guid> Dependencies { get; set; } = new();

        /// <summary>
        /// 依赖此任务的任务ID列表
        /// </summary>
        public List<Guid> Dependents { get; set; } = new();

        /// <summary>
        /// 父任务ID
        /// </summary>
        public Guid? ParentTaskId { get; set; }

        /// <summary>
        /// 子任务ID列表
        /// </summary>
        public List<Guid> ChildTaskIds { get; set; } = new();

        /// <summary>
        /// 所属轮次
        /// </summary>
        public int Round { get; set; } = 1;

        /// <summary>
        /// 执行顺序
        /// </summary>
        public int ExecutionOrder { get; set; } = 0;

        /// <summary>
        /// 是否为根任务
        /// </summary>
        public bool IsRootTask { get; set; } = false;

        /// <summary>
        /// 是否为叶子任务
        /// </summary>
        public bool IsLeafTask { get; set; } = true;

        /// <summary>
        /// 是否可并行执行
        /// </summary>
        public bool CanExecuteInParallel { get; set; } = false;

        /// <summary>
        /// 是否需要用户输入
        /// </summary>
        public bool RequiresUserInput { get; set; } = false;

        /// <summary>
        /// 是否需要外部服务
        /// </summary>
        public bool RequiresExternalService { get; set; } = true;

        /// <summary>
        /// 期望输出类型
        /// </summary>
        public Type? ExpectedOutputType { get; set; }

        /// <summary>
        /// 验证规则
        /// </summary>
        public List<ValidationRule> ValidationRules { get; set; } = new();

        /// <summary>
        /// 转换规则
        /// </summary>
        public List<TransformationRule> TransformationRules { get; set; } = new();

        /// <summary>
        /// 继续条件
        /// </summary>
        public Func<AITask, bool>? ContinuationCondition { get; set; }

        /// <summary>
        /// 终止条件
        /// </summary>
        public Func<AITask, bool>? TerminationCondition { get; set; }

        /// <summary>
        /// 任务标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 任务元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// 任务配置
        /// </summary>
        public TaskConfiguration Configuration { get; set; } = new();

        /// <summary>
        /// 任务执行上下文
        /// </summary>
        public TaskExecutionContext ExecutionContext { get; set; } = new();

        /// <summary>
        /// 任务结果
        /// </summary>
        public TaskResult Result { get; set; } = new();

        /// <summary>
        /// 任务指标
        /// </summary>
        public TaskMetrics Metrics { get; set; } = new();

        /// <summary>
        /// 开始任务
        /// </summary>
        public void Start()
        {
            if (Status != AITalk.Core.Enums.TaskStatus.Pending)
                throw new InvalidOperationException($"Cannot start task in {Status} status");

            Status = AITalk.Core.Enums.TaskStatus.Running;
            StartedAt = DateTime.UtcNow;
            Progress = 0.0;
            
            Metrics.StartTime = StartedAt.Value;
            Metrics.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        public void Complete(object? output = null)
        {
            Status = AITalk.Core.Enums.TaskStatus.Completed;
            CompletedAt = DateTime.UtcNow;
            Progress = 100.0;
            Output = output;
            
            Metrics.EndTime = CompletedAt.Value;
            Metrics.Duration = ActualDuration ?? TimeSpan.Zero;
            Metrics.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 任务失败
        /// </summary>
        public void Fail(Exception error)
        {
            Status = AITalk.Core.Enums.TaskStatus.Failed;
            CompletedAt = DateTime.UtcNow;
            Error = error;
            
            Metrics.EndTime = CompletedAt.Value;
            Metrics.Duration = ActualDuration ?? TimeSpan.Zero;
            Metrics.ErrorCount++;
            Metrics.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        public void Cancel(string reason = "")
        {
            Status = AITalk.Core.Enums.TaskStatus.Cancelled;
            CompletedAt = DateTime.UtcNow;
            
            if (!string.IsNullOrEmpty(reason))
            {
                Metadata["CancellationReason"] = reason;
            }
            
            Metrics.EndTime = CompletedAt.Value;
            Metrics.Duration = ActualDuration ?? TimeSpan.Zero;
            Metrics.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        public void UpdateProgress(double progress, string? statusMessage = null)
        {
            Progress = Math.Max(0, Math.Min(100, progress));
            
            if (!string.IsNullOrEmpty(statusMessage))
            {
                Metadata["StatusMessage"] = statusMessage;
            }
            
            Metrics.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 重试任务
        /// </summary>
        public bool CanRetry()
        {
            return Status == TaskStatus.Failed && RetryCount < MaxRetries;
        }

        /// <summary>
        /// 执行重试
        /// </summary>
        public void Retry()
        {
            if (!CanRetry())
                throw new InvalidOperationException("Task cannot be retried");

            RetryCount++;
            Status = TaskStatus.Pending;
            Error = null;
            Progress = 0.0;
            StartedAt = null;
            CompletedAt = null;
            
            Metrics.RetryCount = RetryCount;
            Metrics.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 检查依赖是否满足
        /// </summary>
        public bool AreDependenciesSatisfied(Dictionary<Guid, AITask> allTasks)
        {
            foreach (var depId in Dependencies)
            {
                if (allTasks.TryGetValue(depId, out var depTask))
                {
                    if (depTask.Status != TaskStatus.Completed)
                        return false;
                }
                else
                {
                    return false; // 依赖任务不存在
                }
            }
            return true;
        }

        /// <summary>
        /// 获取任务持续时间
        /// </summary>
        public TimeSpan GetDuration()
        {
            if (StartedAt.HasValue)
            {
                var endTime = CompletedAt ?? DateTime.UtcNow;
                return endTime - StartedAt.Value;
            }
            return TimeSpan.Zero;
        }

        /// <summary>
        /// 检查是否超时
        /// </summary>
        public bool IsTimedOut()
        {
            if (Configuration.Timeout.HasValue && StartedAt.HasValue)
            {
                return DateTime.UtcNow - StartedAt.Value > Configuration.Timeout.Value;
            }
            return false;
        }

        /// <summary>
        /// 克隆任务
        /// </summary>
        public AITask Clone()
        {
            return new AITask
            {
                TaskId = Guid.NewGuid(),
                TaskType = TaskType,
                TaskName = TaskName + "_Clone",
                Description = Description,
                Priority = Priority,
                Input = Input,
                MaxRetries = MaxRetries,
                Round = Round,
                CanExecuteInParallel = CanExecuteInParallel,
                RequiresUserInput = RequiresUserInput,
                RequiresExternalService = RequiresExternalService,
                ExpectedOutputType = ExpectedOutputType,
                ValidationRules = new List<ValidationRule>(ValidationRules),
                TransformationRules = new List<TransformationRule>(TransformationRules),
                Tags = new List<string>(Tags),
                Metadata = new Dictionary<string, object>(Metadata),
                Configuration = Configuration.Clone()
            };
        }
    }
}
