using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace AITalk.Test
{
    // 简化的数据模型用于测试
    public class SimpleServiceData
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string ServiceName { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = string.Empty;
        public string PrimaryApiKey { get; set; } = string.Empty;
        public string PrimaryIpAddress { get; set; } = string.Empty;
        public int ServerPort { get; set; } = 443;
        public bool UseSSL { get; set; } = true;
        public double HealthScore { get; set; } = 100.0;
        public bool IsHealthy { get; set; } = true;
        public double AverageResponseTimeMs { get; set; } = 0.0;
        public double SuccessRate { get; set; } = 100.0;
        public long TotalRequests { get; set; } = 0;
        public long SuccessfulRequests { get; set; } = 0;
        public long FailedRequests { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        public void UpdatePerformanceMetrics(TimeSpan responseTime, bool isSuccess)
        {
            TotalRequests++;

            if (isSuccess)
            {
                SuccessfulRequests++;
                SuccessRate = (double)SuccessfulRequests / TotalRequests * 100.0;
            }
            else
            {
                FailedRequests++;
                SuccessRate = (double)SuccessfulRequests / TotalRequests * 100.0;
            }

            // 简单的移动平均
            AverageResponseTimeMs = (AverageResponseTimeMs * 0.9) + (responseTime.TotalMilliseconds * 0.1);
            LastUpdated = DateTime.UtcNow;
        }

        public void UpdateHealth(bool isHealthy, string message = "")
        {
            IsHealthy = isHealthy;
            if (isHealthy)
            {
                HealthScore = Math.Min(100.0, HealthScore + 5.0);
            }
            else
            {
                HealthScore = Math.Max(0.0, HealthScore - 10.0);
            }
            LastUpdated = DateTime.UtcNow;
        }
    }

    public enum AITalkStatus
    {
        Created,
        WaitingForParent,  // 等待父任务完成
        Running,
        Completed,
        Failed,
        Cancelled
    }

    public enum ExecutionMode
    {
        WaitForParent,     // 等待父任务完成后再执行
        ConcurrentWithParent,  // 与父任务并发执行
        Sequential,        // 按优先级顺序执行
        Concurrent         // 同优先级并发执行
    }

    public class AITalk
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;

        // === 核心数据 ===
        /// <summary>
        /// 用户输入的Prompt
        /// </summary>
        public string UserPrompt { get; set; } = string.Empty;

        /// <summary>
        /// AI返回的数据
        /// </summary>
        public string AIResponse { get; set; } = string.Empty;

        /// <summary>
        /// 任务状态
        /// </summary>
        public AITalkStatus Status { get; set; } = AITalkStatus.Created;

        // === 关联关系 ===
        /// <summary>
        /// 父AITalk引用 - 子任务可以访问父任务的所有数据
        /// </summary>
        public AITalk? ParentAITalk { get; set; }

        /// <summary>
        /// 子AITalk列表
        /// </summary>
        public List<AITalk> SubAITalks { get; set; } = new();

        // === 调度控制 ===
        /// <summary>
        /// 优先级 - 数字越大优先级越高
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 执行模式
        /// </summary>
        public ExecutionMode ExecutionMode { get; set; } = ExecutionMode.WaitForParent;

        /// <summary>
        /// 最大并发子任务数
        /// </summary>
        public int MaxConcurrentSubTasks { get; set; } = 3;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 添加子AITalk
        /// </summary>
        public void AddSubAITalk(AITalk subAITalk)
        {
            subAITalk.ParentAITalk = this;
            SubAITalks.Add(subAITalk);
        }

        /// <summary>
        /// 开始执行
        /// </summary>
        public void Start()
        {
            Status = AITalkStatus.Running;
            StartedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        public void Complete()
        {
            Status = AITalkStatus.Completed;
            CompletedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// 任务失败
        /// </summary>
        public void Fail()
        {
            Status = AITalkStatus.Failed;
            CompletedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// 获取父任务的Prompt - 子任务可以访问父任务数据
        /// </summary>
        public string GetParentPrompt()
        {
            return ParentAITalk?.UserPrompt ?? string.Empty;
        }

        /// <summary>
        /// 获取父任务的AI响应 - 子任务可以访问父任务数据
        /// </summary>
        public string GetParentAIResponse()
        {
            return ParentAITalk?.AIResponse ?? string.Empty;
        }

        /// <summary>
        /// 获取父任务的状态 - 子任务可以访问父任务状态
        /// </summary>
        public AITalkStatus GetParentStatus()
        {
            return ParentAITalk?.Status ?? AITalkStatus.Completed;
        }

        /// <summary>
        /// 检查是否可以开始执行
        /// </summary>
        public bool CanStart()
        {
            switch (ExecutionMode)
            {
                case ExecutionMode.WaitForParent:
                    return ParentAITalk == null || ParentAITalk.Status == AITalkStatus.Completed;

                case ExecutionMode.ConcurrentWithParent:
                    return ParentAITalk == null || ParentAITalk.Status == AITalkStatus.Running;

                default:
                    return true;
            }
        }

        /// <summary>
        /// 获取可以并发执行的子任务组
        /// </summary>
        public List<List<AITalk>> GetConcurrentGroups()
        {
            var groups = new List<List<AITalk>>();

            // 按优先级分组
            var priorityGroups = SubAITalks
                .Where(t => t.Status == AITalkStatus.Created)
                .GroupBy(t => t.Priority)
                .OrderByDescending(g => g.Key); // 优先级高的先执行

            foreach (var priorityGroup in priorityGroups)
            {
                var tasks = priorityGroup.ToList();

                // 检查执行模式
                var concurrentTasks = tasks.Where(t =>
                    t.ExecutionMode == ExecutionMode.Concurrent ||
                    t.ExecutionMode == ExecutionMode.ConcurrentWithParent).ToList();

                var sequentialTasks = tasks.Where(t =>
                    t.ExecutionMode == ExecutionMode.Sequential ||
                    t.ExecutionMode == ExecutionMode.WaitForParent).ToList();

                // 并发任务作为一组
                if (concurrentTasks.Count > 0)
                {
                    groups.Add(concurrentTasks);
                }

                // 顺序任务每个单独一组
                foreach (var task in sequentialTasks)
                {
                    groups.Add(new List<AITalk> { task });
                }
            }

            return groups;
        }

        /// <summary>
        /// 获取执行时间
        /// </summary>
        public TimeSpan GetExecutionTime()
        {
            if (StartedAt == null) return TimeSpan.Zero;
            var endTime = CompletedAt ?? DateTime.UtcNow;
            return endTime - StartedAt.Value;
        }

        /// <summary>
        /// 获取所有子任务状态统计
        /// </summary>
        public (int created, int waiting, int running, int completed, int failed) GetSubTaskStats()
        {
            return (
                SubAITalks.Count(t => t.Status == AITalkStatus.Created),
                SubAITalks.Count(t => t.Status == AITalkStatus.WaitingForParent),
                SubAITalks.Count(t => t.Status == AITalkStatus.Running),
                SubAITalks.Count(t => t.Status == AITalkStatus.Completed),
                SubAITalks.Count(t => t.Status == AITalkStatus.Failed)
            );
        }

    }

    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== AITalk Multi-Round Conversation & Concurrency Test ===");
            Console.WriteLine("Testing multi-round conversation with concurrent task processing");
            Console.WriteLine();

            // 测试多轮对话和并发处理
            await TestMultiRoundConversationWithConcurrency();
            Console.WriteLine();

            Console.WriteLine("All tests completed successfully! ✅");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static async Task TestMultiRoundConversationWithConcurrency()
        {
            Console.WriteLine("🚀 Starting AITalk Priority-Based Scheduling Test");
            Console.WriteLine("Testing: Parent-Child relationship, Priority scheduling, Concurrent vs Sequential execution");

            // 创建DeepSeek服务
            var service = new SimpleServiceData
            {
                ServiceName = "DeepSeek API",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22"
            };

            // 创建主AITalk
            var mainAITalk = new AITalk
            {
                Name = "Main AITalk - AI Knowledge Query",
                UserId = "test_user",
                UserPrompt = "请详细介绍一下人工智能的发展历史和主要应用领域。",
                MaxConcurrentSubTasks = 3
            };

            Console.WriteLine($"✅ Created main AITalk: {mainAITalk.Name}");
            Console.WriteLine($"   ID: {mainAITalk.Id}");
            Console.WriteLine($"   User Prompt: {mainAITalk.UserPrompt}");
            Console.WriteLine($"   Status: {mainAITalk.Status}");
            Console.WriteLine();

            // 第一轮：执行主任务
            Console.WriteLine("📝 Round 1: Executing main AITalk");
            mainAITalk.Start();

            var initialResponse = await CallDeepSeekAPI(service, "deepseek-chat", mainAITalk.UserPrompt);
            mainAITalk.AIResponse = initialResponse;
            mainAITalk.Complete();

            Console.WriteLine($"✅ Main AITalk completed ({initialResponse.Length} characters)");
            Console.WriteLine($"   Status: {mainAITalk.Status}");
            Console.WriteLine($"   Execution Time: {mainAITalk.GetExecutionTime().TotalSeconds:F1}s");
            Console.WriteLine();

            // 第二轮：创建子AITalk任务，展示不同的优先级和执行模式
            Console.WriteLine("🔄 Round 2: Creating SubAITalks with different priorities and execution modes");

            // 高优先级任务 (Priority = 3) - 等待父任务完成后执行
            var summaryTask = new AITalk
            {
                Name = "High Priority: Create Summary",
                UserId = "test_user",
                Priority = 3,
                ExecutionMode = ExecutionMode.WaitForParent,
                UserPrompt = $"请为以下内容创建一个简洁的摘要：\n\n{mainAITalk.AIResponse}"
            };
            mainAITalk.AddSubAITalk(summaryTask);

            // 中优先级任务 (Priority = 2) - 并发执行
            var mdTask = new AITalk
            {
                Name = "Medium Priority: Convert to Markdown",
                UserId = "test_user",
                Priority = 2,
                ExecutionMode = ExecutionMode.Concurrent,
                UserPrompt = $"请将以下内容转换为Markdown格式：\n\n{mainAITalk.AIResponse}"
            };
            mainAITalk.AddSubAITalk(mdTask);

            var htmlTask = new AITalk
            {
                Name = "Medium Priority: Convert to HTML",
                UserId = "test_user",
                Priority = 2,
                ExecutionMode = ExecutionMode.Concurrent,
                UserPrompt = $"请将以下内容转换为HTML格式：\n\n{mainAITalk.AIResponse}"
            };
            mainAITalk.AddSubAITalk(htmlTask);

            // 低优先级任务 (Priority = 1) - 顺序执行
            var keywordsTask = new AITalk
            {
                Name = "Low Priority: Extract Keywords",
                UserId = "test_user",
                Priority = 1,
                ExecutionMode = ExecutionMode.Sequential,
                UserPrompt = $"请从以下内容中提取关键词：\n\n{mainAITalk.AIResponse}"
            };
            mainAITalk.AddSubAITalk(keywordsTask);

            Console.WriteLine($"✅ Added {mainAITalk.SubAITalks.Count} SubAITalks to main AITalk:");
            foreach (var subAITalk in mainAITalk.SubAITalks)
            {
                Console.WriteLine($"   - {subAITalk.Name}");
                Console.WriteLine($"     Priority: {subAITalk.Priority}, Mode: {subAITalk.ExecutionMode}");
                Console.WriteLine($"     Can access parent prompt: {!string.IsNullOrEmpty(subAITalk.GetParentPrompt())}");
                Console.WriteLine($"     Can access parent response: {!string.IsNullOrEmpty(subAITalk.GetParentAIResponse())}");
                Console.WriteLine($"     Parent status: {subAITalk.GetParentStatus()}");
                Console.WriteLine();
            }

            // 第三轮：按优先级和执行模式调度执行
            Console.WriteLine("⚡ Round 3: Executing SubAITalks with priority-based scheduling");
            var startTime = DateTime.UtcNow;

            // 获取并发执行组
            var concurrentGroups = mainAITalk.GetConcurrentGroups();
            Console.WriteLine($"� Execution plan: {concurrentGroups.Count} groups");

            for (int groupIndex = 0; groupIndex < concurrentGroups.Count; groupIndex++)
            {
                var group = concurrentGroups[groupIndex];
                Console.WriteLine($"\n🔄 Executing Group {groupIndex + 1}: {group.Count} tasks");

                if (group.Count == 1)
                {
                    // 单个任务 - 顺序执行
                    var task = group[0];
                    Console.WriteLine($"   Sequential: {task.Name} (Priority: {task.Priority})");
                    await ExecuteAITalk(service, task);
                }
                else
                {
                    // 多个任务 - 并发执行
                    Console.WriteLine($"   Concurrent: {string.Join(", ", group.Select(t => t.Name))} (Priority: {group[0].Priority})");
                    var concurrentTasks = group.Select(task => ExecuteAITalk(service, task)).ToArray();
                    await Task.WhenAll(concurrentTasks);
                }
            }

            var totalDuration = DateTime.UtcNow - startTime;
            Console.WriteLine();
            Console.WriteLine($"🎯 All SubAITalks completed in {totalDuration.TotalSeconds:F1}s");

            // 显示每个子AITalk的结果
            Console.WriteLine("� SubAITalk Results:");
            for (int i = 0; i < mainAITalk.SubAITalks.Count; i++)
            {
                var subAITalk = mainAITalk.SubAITalks[i];
                var responseLength = subAITalk.AIResponse.Length;

                Console.WriteLine($"   SubAITalk {i + 1}: {subAITalk.Name}");
                Console.WriteLine($"   - ID: {subAITalk.Id}");
                Console.WriteLine($"   - Status: {subAITalk.Status}");
                Console.WriteLine($"   - Priority: {subAITalk.Priority}");
                Console.WriteLine($"   - Response length: {responseLength} characters");
                Console.WriteLine($"   - Execution time: {subAITalk.GetExecutionTime().TotalSeconds:F1}s");
                Console.WriteLine($"   - Preview: {subAITalk.AIResponse[..Math.Min(100, responseLength)]}...");
                Console.WriteLine();
            }

            // 显示性能统计
            Console.WriteLine("📈 Performance Statistics:");
            Console.WriteLine($"   - Total requests: {service.TotalRequests}");
            Console.WriteLine($"   - Success rate: {service.SuccessRate:F1}%");
            Console.WriteLine($"   - Average response time: {service.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   - Main AITalk SubTasks: {mainAITalk.SubAITalks.Count}");
            Console.WriteLine($"   - Total execution time: {totalDuration.TotalSeconds:F1}s");
            Console.WriteLine($"   - Concurrency efficiency: {(mainAITalk.SubAITalks.Count * service.AverageResponseTimeMs / 1000.0 / totalDuration.TotalSeconds):F1}x");
        }

        static async Task ExecuteAITalk(SimpleServiceData service, AITalk aiTalk)
        {
            Console.WriteLine($"🔄 Starting {aiTalk.Name}");
            aiTalk.Start();

            try
            {
                var response = await CallDeepSeekAPI(service, "deepseek-chat", aiTalk.UserPrompt);
                aiTalk.AIResponse = response;
                aiTalk.Complete();

                var duration = aiTalk.GetExecutionTime();
                Console.WriteLine($"✅ {aiTalk.Name} completed in {duration.TotalSeconds:F1}s");
            }
            catch (Exception ex)
            {
                aiTalk.Fail();
                var duration = aiTalk.GetExecutionTime();
                Console.WriteLine($"❌ {aiTalk.Name} failed in {duration.TotalSeconds:F1}s: {ex.Message}");
            }
        }

        static async Task<string> CallDeepSeekAPI(SimpleServiceData service, string model, string prompt)
        {
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {service.PrimaryApiKey}");

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 2000,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var startTime = DateTime.UtcNow;
                var response = await httpClient.PostAsync($"{service.BaseUrl}/chat/completions", content);
                var duration = DateTime.UtcNow - startTime;

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var responseJson = JsonDocument.Parse(responseContent);

                    if (responseJson.RootElement.TryGetProperty("choices", out var choices) &&
                        choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var messageContent))
                        {
                            // 更新服务性能指标
                            service.UpdatePerformanceMetrics(duration, true);
                            service.UpdateHealth(true, $"Successfully called {model}");

                            return messageContent.GetString() ?? "Empty response";
                        }
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    service.UpdatePerformanceMetrics(duration, false);
                    service.UpdateHealth(false, $"Failed to call {model}: {response.StatusCode}");
                    return $"API Error: {response.StatusCode} - {errorContent}";
                }
            }
            catch (Exception ex)
            {
                service.UpdateHealth(false, $"Exception calling {model}: {ex.Message}");
                return $"Exception: {ex.Message}";
            }

            return "No response received";
        }

        static async Task TestServiceData()
        {
            Console.WriteLine("--- Testing SimpleServiceData (New Architecture) ---");

            // 创建DeepSeek服务配置 - 展示新的详细数据结构
            var serviceData = new SimpleServiceData
            {
                ServiceName = "DeepSeek API Service",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22",
                PrimaryIpAddress = "************",
                ServerPort = 443,
                UseSSL = true,
                IsHealthy = true,
                HealthScore = 100.0
            };

            Console.WriteLine($"✅ Service Created: {serviceData.ServiceName}");
            Console.WriteLine($"   ID: {serviceData.Id}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   Primary IP: {serviceData.PrimaryIpAddress}:{serviceData.ServerPort}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey[..10]}...");
            Console.WriteLine($"   Health Score: {serviceData.HealthScore}%");
            Console.WriteLine($"   SSL Enabled: {serviceData.UseSSL}");

            // 测试性能指标更新 - 展示新架构的功能
            Console.WriteLine("\n   Testing performance metrics update:");
            serviceData.UpdatePerformanceMetrics(TimeSpan.FromMilliseconds(1500), true);
            Console.WriteLine($"   ✓ Updated metrics - Response Time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   ✓ Success Rate: {serviceData.SuccessRate:F1}%");
            Console.WriteLine($"   ✓ Total Requests: {serviceData.TotalRequests}");

            // 测试健康状态更新
            Console.WriteLine("\n   Testing health status update:");
            serviceData.UpdateHealth(true, "Service is healthy and ready");
            Console.WriteLine($"   ✓ Health Status: {(serviceData.IsHealthy ? "Healthy" : "Unhealthy")}");
            Console.WriteLine($"   ✓ Health Score: {serviceData.HealthScore}%");

            await Task.CompletedTask;
        }

        static async Task TestConversationData()
        {
            Console.WriteLine("--- Testing AITalk (New Architecture) ---");

            // 创建对话数据 - 展示新的多轮调度和自循环控制架构
            var conversationData = new AITalk
            {
                Name = "AI Assistant Multi-Round Conversation",
                UserId = "user123",
                Priority = 1,
                ExecutionMode = ExecutionMode.WaitForParent,
                MaxConcurrentSubTasks = 3
            };

            Console.WriteLine($"✅ Conversation Created: {conversationData.ConversationName}");
            Console.WriteLine($"   ID: {conversationData.ConversationId}");
            Console.WriteLine($"   User: {conversationData.UserId}");
            Console.WriteLine($"   Max Rounds: {conversationData.MaxRounds}");
            Console.WriteLine($"   Max Loop Iterations: {conversationData.MaxLoopIterations}");
            Console.WriteLine($"   Max Concurrent Tasks: {conversationData.MaxConcurrentTasks}");

            // 测试消息添加 - 展示新架构的消息管理
            Console.WriteLine("\n   Testing message management:");
            conversationData.AddMessage("User: Hello, I need help with planning a trip to Japan.");
            Console.WriteLine($"   ✓ Added user message");

            conversationData.AddMessage("Assistant: I'd be happy to help you plan your trip to Japan!");
            Console.WriteLine($"   ✓ Added assistant message");

            // 测试任务添加 - 展示新架构的多轮任务调度
            Console.WriteLine("\n   Testing multi-round task scheduling:");
            conversationData.AddTask("Task 1: Analyze User Request (Round 1)");
            Console.WriteLine($"   ✓ Added analysis task");

            conversationData.AddTask("Task 2: Gather Information (Round 1)");
            Console.WriteLine($"   ✓ Added information gathering task");

            conversationData.AddTask("Task 3: Generate Recommendations (Round 2)");
            Console.WriteLine($"   ✓ Added recommendation task");

            // 展示数据统计
            Console.WriteLine("\n   Current conversation statistics:");
            Console.WriteLine($"   ✓ Total messages: {conversationData.Messages.Count}");
            Console.WriteLine($"   ✓ Total tasks: {conversationData.Tasks.Count}");
            Console.WriteLine($"   ✓ Created at: {conversationData.CreatedAt:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"   ✓ Last updated: {conversationData.LastUpdated:yyyy-MM-dd HH:mm:ss}");

            await Task.CompletedTask;
        }

        static async Task TestRealDeepSeekAPI()
        {
            Console.WriteLine("--- Testing Real DeepSeek API ---");
            Console.WriteLine("This demonstrates the new architecture with actual API calls");

            var serviceData = new SimpleServiceData
            {
                ServiceName = "DeepSeek API",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22"
            };

            Console.WriteLine($"✅ Testing with service: {serviceData.ServiceName}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey[..10]}...");

            // 测试deepseek-chat模型
            Console.WriteLine("\n   Testing deepseek-chat model:");
            await TestModel(serviceData, "deepseek-chat", "你好，请简单介绍一下你自己。");

            Console.WriteLine("\n   Testing deepseek-reasoner model:");
            await TestModel(serviceData, "deepseek-reasoner", "请解释一下什么是人工智能。");

            // 展示服务性能统计
            Console.WriteLine("\n   Final service statistics:");
            Console.WriteLine($"   ✓ Total requests: {serviceData.TotalRequests}");
            Console.WriteLine($"   ✓ Successful requests: {serviceData.SuccessfulRequests}");
            Console.WriteLine($"   ✓ Failed requests: {serviceData.FailedRequests}");
            Console.WriteLine($"   ✓ Success rate: {serviceData.SuccessRate:F1}%");
            Console.WriteLine($"   ✓ Average response time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   ✓ Health score: {serviceData.HealthScore:F1}%");
        }

        static async Task TestModel(SimpleServiceData service, string model, string prompt)
        {
            Console.WriteLine($"Testing model: {model}");
            Console.WriteLine($"Prompt: {prompt}");

            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {service.PrimaryApiKey}");

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 500,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var startTime = DateTime.UtcNow;
                var response = await httpClient.PostAsync($"{service.BaseUrl}/chat/completions", content);
                var duration = DateTime.UtcNow - startTime;

                Console.WriteLine($"Response Status: {response.StatusCode}");
                Console.WriteLine($"Response Time: {duration.TotalMilliseconds:F0}ms");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var responseJson = JsonDocument.Parse(responseContent);
                    
                    if (responseJson.RootElement.TryGetProperty("choices", out var choices) && 
                        choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var messageContent))
                        {
                            Console.WriteLine($"Response: {messageContent.GetString()}");
                        }
                    }

                    // 更新服务性能指标
                    service.UpdatePerformanceMetrics(duration, true);
                    service.UpdateHealth(true, $"Successfully called {model}");
                    
                    Console.WriteLine($"✅ API call successful for {model}");
                    Console.WriteLine($"Updated metrics - Avg Response: {service.AverageResponseTimeMs:F1}ms, Success Rate: {service.SuccessRate:F1}%");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"❌ API call failed: {errorContent}");
                    
                    service.UpdatePerformanceMetrics(duration, false);
                    service.UpdateHealth(false, $"Failed to call {model}: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Exception occurred: {ex.Message}");
                service.UpdateHealth(false, $"Exception calling {model}: {ex.Message}");
            }
        }
    }
}
