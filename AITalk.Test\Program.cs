using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace AITalk.Test
{
    // 简化的数据模型用于测试
    public class SimpleServiceData
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string ServiceName { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = string.Empty;
        public string PrimaryApiKey { get; set; } = string.Empty;
        public string PrimaryIpAddress { get; set; } = string.Empty;
        public int ServerPort { get; set; } = 443;
        public bool UseSSL { get; set; } = true;
        public double HealthScore { get; set; } = 100.0;
        public bool IsHealthy { get; set; } = true;
        public double AverageResponseTimeMs { get; set; } = 0.0;
        public double SuccessRate { get; set; } = 100.0;
        public long TotalRequests { get; set; } = 0;
        public long SuccessfulRequests { get; set; } = 0;
        public long FailedRequests { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        public void UpdatePerformanceMetrics(TimeSpan responseTime, bool isSuccess)
        {
            TotalRequests++;

            if (isSuccess)
            {
                SuccessfulRequests++;
                SuccessRate = (double)SuccessfulRequests / TotalRequests * 100.0;
            }
            else
            {
                FailedRequests++;
                SuccessRate = (double)SuccessfulRequests / TotalRequests * 100.0;
            }

            // 简单的移动平均
            AverageResponseTimeMs = (AverageResponseTimeMs * 0.9) + (responseTime.TotalMilliseconds * 0.1);
            LastUpdated = DateTime.UtcNow;
        }

        public void UpdateHealth(bool isHealthy, string message = "")
        {
            IsHealthy = isHealthy;
            if (isHealthy)
            {
                HealthScore = Math.Min(100.0, HealthScore + 5.0);
            }
            else
            {
                HealthScore = Math.Max(0.0, HealthScore - 10.0);
            }
            LastUpdated = DateTime.UtcNow;
        }
    }

    public class SimpleConversationData
    {
        public Guid ConversationId { get; set; } = Guid.NewGuid();
        public string ConversationName { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public int MaxRounds { get; set; } = 10;
        public int MaxLoopIterations { get; set; } = 5;
        public int MaxConcurrentTasks { get; set; } = 3;
        public List<string> Messages { get; set; } = new();
        public List<string> Tasks { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        public void AddMessage(string message)
        {
            Messages.Add(message);
            LastUpdated = DateTime.UtcNow;
        }

        public void AddTask(string task)
        {
            Tasks.Add(task);
            LastUpdated = DateTime.UtcNow;
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== AITalk Multi-Round Conversation & Concurrency Test ===");
            Console.WriteLine("Testing multi-round conversation with concurrent task processing");
            Console.WriteLine();

            // 测试多轮对话和并发处理
            await TestMultiRoundConversationWithConcurrency();
            Console.WriteLine();

            Console.WriteLine("All tests completed successfully! ✅");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static async Task TestMultiRoundConversationWithConcurrency()
        {
            Console.WriteLine("🚀 Starting Multi-Round Conversation Test with Concurrency");

            // 创建DeepSeek服务
            var service = new SimpleServiceData
            {
                ServiceName = "DeepSeek API",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22"
            };

            // 创建主对话
            var mainConversation = new SimpleConversationData
            {
                ConversationName = "Multi-Round AI Conversation",
                UserId = "test_user",
                MaxConcurrentTasks = 3
            };

            Console.WriteLine($"✅ Created main conversation: {mainConversation.ConversationName}");
            Console.WriteLine($"   Max concurrent tasks: {mainConversation.MaxConcurrentTasks}");
            Console.WriteLine();

            // 第一轮：问一个问题
            Console.WriteLine("📝 Round 1: Asking initial question");
            var initialQuestion = "请详细介绍一下人工智能的发展历史和主要应用领域。";
            mainConversation.AddMessage($"User: {initialQuestion}");

            var initialResponse = await CallDeepSeekAPI(service, "deepseek-chat", initialQuestion);
            mainConversation.AddMessage($"Assistant: {initialResponse}");

            Console.WriteLine($"✅ Got initial response ({initialResponse.Length} characters)");
            Console.WriteLine();

            // 第二轮：创建多个AITalk任务并并发执行
            Console.WriteLine("🔄 Round 2: Creating multiple AITalk tasks for concurrent processing");

            var aiTalkList = new List<SimpleConversationData>();
            var concurrentTasks = new List<Task>();

            // 创建MD格式转换任务
            var mdTask = new SimpleConversationData
            {
                ConversationName = "Convert to Markdown",
                UserId = "test_user"
            };
            mdTask.AddMessage($"User: 请将以下内容转换为Markdown格式：\n\n{initialResponse}");
            aiTalkList.Add(mdTask);

            // 创建HTML格式转换任务
            var htmlTask = new SimpleConversationData
            {
                ConversationName = "Convert to HTML",
                UserId = "test_user"
            };
            htmlTask.AddMessage($"User: 请将以下内容转换为HTML格式：\n\n{initialResponse}");
            aiTalkList.Add(htmlTask);

            // 创建总结任务
            var summaryTask = new SimpleConversationData
            {
                ConversationName = "Create Summary",
                UserId = "test_user"
            };
            summaryTask.AddMessage($"User: 请为以下内容创建一个简洁的摘要：\n\n{initialResponse}");
            aiTalkList.Add(summaryTask);

            Console.WriteLine($"✅ Created {aiTalkList.Count} AITalk tasks");
            Console.WriteLine("   - Markdown conversion task");
            Console.WriteLine("   - HTML conversion task");
            Console.WriteLine("   - Summary creation task");
            Console.WriteLine();

            // 并发执行所有任务
            Console.WriteLine("⚡ Starting concurrent execution of all tasks...");
            var startTime = DateTime.UtcNow;

            // 为每个AITalk创建并发任务
            for (int i = 0; i < aiTalkList.Count; i++)
            {
                var taskIndex = i;
                var aiTalk = aiTalkList[taskIndex];

                var task = Task.Run(async () =>
                {
                    var taskStartTime = DateTime.UtcNow;
                    Console.WriteLine($"🔄 Task {taskIndex + 1} ({aiTalk.ConversationName}) started");

                    try
                    {
                        var lastMessage = aiTalk.Messages.Last();
                        var prompt = lastMessage.Replace("User: ", "");

                        var response = await CallDeepSeekAPI(service, "deepseek-chat", prompt);
                        aiTalk.AddMessage($"Assistant: {response}");

                        var duration = DateTime.UtcNow - taskStartTime;
                        Console.WriteLine($"✅ Task {taskIndex + 1} ({aiTalk.ConversationName}) completed in {duration.TotalSeconds:F1}s");

                        return new { TaskIndex = taskIndex + 1, Name = aiTalk.ConversationName, Response = response, Duration = duration };
                    }
                    catch (Exception ex)
                    {
                        var duration = DateTime.UtcNow - taskStartTime;
                        Console.WriteLine($"❌ Task {taskIndex + 1} ({aiTalk.ConversationName}) failed in {duration.TotalSeconds:F1}s: {ex.Message}");
                        return new { TaskIndex = taskIndex + 1, Name = aiTalk.ConversationName, Response = $"Error: {ex.Message}", Duration = duration };
                    }
                });

                concurrentTasks.Add(task);
            }

            // 等待所有任务完成
            await Task.WhenAll(concurrentTasks);
            var totalDuration = DateTime.UtcNow - startTime;

            Console.WriteLine();
            Console.WriteLine($"🎯 All concurrent tasks completed in {totalDuration.TotalSeconds:F1}s");
            Console.WriteLine();

            // 显示结果
            Console.WriteLine("📊 Results Summary:");
            for (int i = 0; i < aiTalkList.Count; i++)
            {
                var aiTalk = aiTalkList[i];
                var lastMessage = aiTalk.Messages.Last();
                var responseLength = lastMessage.Length;

                Console.WriteLine($"   Task {i + 1}: {aiTalk.ConversationName}");
                Console.WriteLine($"   - Messages: {aiTalk.Messages.Count}");
                Console.WriteLine($"   - Response length: {responseLength} characters");
                Console.WriteLine($"   - Preview: {lastMessage.Substring(0, Math.Min(100, responseLength))}...");
                Console.WriteLine();
            }

            // 显示性能统计
            Console.WriteLine("📈 Performance Statistics:");
            Console.WriteLine($"   - Total requests: {service.TotalRequests}");
            Console.WriteLine($"   - Success rate: {service.SuccessRate:F1}%");
            Console.WriteLine($"   - Average response time: {service.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   - Concurrent tasks: {aiTalkList.Count}");
            Console.WriteLine($"   - Total execution time: {totalDuration.TotalSeconds:F1}s");
            Console.WriteLine($"   - Concurrency efficiency: {(aiTalkList.Count * service.AverageResponseTimeMs / 1000.0 / totalDuration.TotalSeconds):F1}x");
        }

        static async Task<string> CallDeepSeekAPI(SimpleServiceData service, string model, string prompt)
        {
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {service.PrimaryApiKey}");

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 2000,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var startTime = DateTime.UtcNow;
                var response = await httpClient.PostAsync($"{service.BaseUrl}/chat/completions", content);
                var duration = DateTime.UtcNow - startTime;

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var responseJson = JsonDocument.Parse(responseContent);

                    if (responseJson.RootElement.TryGetProperty("choices", out var choices) &&
                        choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var messageContent))
                        {
                            // 更新服务性能指标
                            service.UpdatePerformanceMetrics(duration, true);
                            service.UpdateHealth(true, $"Successfully called {model}");

                            return messageContent.GetString() ?? "Empty response";
                        }
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    service.UpdatePerformanceMetrics(duration, false);
                    service.UpdateHealth(false, $"Failed to call {model}: {response.StatusCode}");
                    return $"API Error: {response.StatusCode} - {errorContent}";
                }
            }
            catch (Exception ex)
            {
                service.UpdateHealth(false, $"Exception calling {model}: {ex.Message}");
                return $"Exception: {ex.Message}";
            }

            return "No response received";
        }

        static async Task TestServiceData()
        {
            Console.WriteLine("--- Testing SimpleServiceData (New Architecture) ---");

            // 创建DeepSeek服务配置 - 展示新的详细数据结构
            var serviceData = new SimpleServiceData
            {
                ServiceName = "DeepSeek API Service",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22",
                PrimaryIpAddress = "************",
                ServerPort = 443,
                UseSSL = true,
                IsHealthy = true,
                HealthScore = 100.0
            };

            Console.WriteLine($"✅ Service Created: {serviceData.ServiceName}");
            Console.WriteLine($"   ID: {serviceData.Id}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   Primary IP: {serviceData.PrimaryIpAddress}:{serviceData.ServerPort}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey[..10]}...");
            Console.WriteLine($"   Health Score: {serviceData.HealthScore}%");
            Console.WriteLine($"   SSL Enabled: {serviceData.UseSSL}");

            // 测试性能指标更新 - 展示新架构的功能
            Console.WriteLine("\n   Testing performance metrics update:");
            serviceData.UpdatePerformanceMetrics(TimeSpan.FromMilliseconds(1500), true);
            Console.WriteLine($"   ✓ Updated metrics - Response Time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   ✓ Success Rate: {serviceData.SuccessRate:F1}%");
            Console.WriteLine($"   ✓ Total Requests: {serviceData.TotalRequests}");

            // 测试健康状态更新
            Console.WriteLine("\n   Testing health status update:");
            serviceData.UpdateHealth(true, "Service is healthy and ready");
            Console.WriteLine($"   ✓ Health Status: {(serviceData.IsHealthy ? "Healthy" : "Unhealthy")}");
            Console.WriteLine($"   ✓ Health Score: {serviceData.HealthScore}%");

            await Task.CompletedTask;
        }

        static async Task TestConversationData()
        {
            Console.WriteLine("--- Testing SimpleConversationData (New Architecture) ---");

            // 创建对话数据 - 展示新的多轮调度和自循环控制架构
            var conversationData = new SimpleConversationData
            {
                ConversationName = "AI Assistant Multi-Round Conversation",
                UserId = "user123",
                MaxRounds = 10,
                MaxLoopIterations = 5,
                MaxConcurrentTasks = 3
            };

            Console.WriteLine($"✅ Conversation Created: {conversationData.ConversationName}");
            Console.WriteLine($"   ID: {conversationData.ConversationId}");
            Console.WriteLine($"   User: {conversationData.UserId}");
            Console.WriteLine($"   Max Rounds: {conversationData.MaxRounds}");
            Console.WriteLine($"   Max Loop Iterations: {conversationData.MaxLoopIterations}");
            Console.WriteLine($"   Max Concurrent Tasks: {conversationData.MaxConcurrentTasks}");

            // 测试消息添加 - 展示新架构的消息管理
            Console.WriteLine("\n   Testing message management:");
            conversationData.AddMessage("User: Hello, I need help with planning a trip to Japan.");
            Console.WriteLine($"   ✓ Added user message");

            conversationData.AddMessage("Assistant: I'd be happy to help you plan your trip to Japan!");
            Console.WriteLine($"   ✓ Added assistant message");

            // 测试任务添加 - 展示新架构的多轮任务调度
            Console.WriteLine("\n   Testing multi-round task scheduling:");
            conversationData.AddTask("Task 1: Analyze User Request (Round 1)");
            Console.WriteLine($"   ✓ Added analysis task");

            conversationData.AddTask("Task 2: Gather Information (Round 1)");
            Console.WriteLine($"   ✓ Added information gathering task");

            conversationData.AddTask("Task 3: Generate Recommendations (Round 2)");
            Console.WriteLine($"   ✓ Added recommendation task");

            // 展示数据统计
            Console.WriteLine("\n   Current conversation statistics:");
            Console.WriteLine($"   ✓ Total messages: {conversationData.Messages.Count}");
            Console.WriteLine($"   ✓ Total tasks: {conversationData.Tasks.Count}");
            Console.WriteLine($"   ✓ Created at: {conversationData.CreatedAt:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"   ✓ Last updated: {conversationData.LastUpdated:yyyy-MM-dd HH:mm:ss}");

            await Task.CompletedTask;
        }

        static async Task TestRealDeepSeekAPI()
        {
            Console.WriteLine("--- Testing Real DeepSeek API ---");
            Console.WriteLine("This demonstrates the new architecture with actual API calls");

            var serviceData = new SimpleServiceData
            {
                ServiceName = "DeepSeek API",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22"
            };

            Console.WriteLine($"✅ Testing with service: {serviceData.ServiceName}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey[..10]}...");

            // 测试deepseek-chat模型
            Console.WriteLine("\n   Testing deepseek-chat model:");
            await TestModel(serviceData, "deepseek-chat", "你好，请简单介绍一下你自己。");

            Console.WriteLine("\n   Testing deepseek-reasoner model:");
            await TestModel(serviceData, "deepseek-reasoner", "请解释一下什么是人工智能。");

            // 展示服务性能统计
            Console.WriteLine("\n   Final service statistics:");
            Console.WriteLine($"   ✓ Total requests: {serviceData.TotalRequests}");
            Console.WriteLine($"   ✓ Successful requests: {serviceData.SuccessfulRequests}");
            Console.WriteLine($"   ✓ Failed requests: {serviceData.FailedRequests}");
            Console.WriteLine($"   ✓ Success rate: {serviceData.SuccessRate:F1}%");
            Console.WriteLine($"   ✓ Average response time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   ✓ Health score: {serviceData.HealthScore:F1}%");
        }

        static async Task TestModel(SimpleServiceData service, string model, string prompt)
        {
            Console.WriteLine($"Testing model: {model}");
            Console.WriteLine($"Prompt: {prompt}");

            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {service.PrimaryApiKey}");

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 500,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var startTime = DateTime.UtcNow;
                var response = await httpClient.PostAsync($"{service.BaseUrl}/chat/completions", content);
                var duration = DateTime.UtcNow - startTime;

                Console.WriteLine($"Response Status: {response.StatusCode}");
                Console.WriteLine($"Response Time: {duration.TotalMilliseconds:F0}ms");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var responseJson = JsonDocument.Parse(responseContent);
                    
                    if (responseJson.RootElement.TryGetProperty("choices", out var choices) && 
                        choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var messageContent))
                        {
                            Console.WriteLine($"Response: {messageContent.GetString()}");
                        }
                    }

                    // 更新服务性能指标
                    service.UpdatePerformanceMetrics(duration, true);
                    service.UpdateHealth(true, $"Successfully called {model}");
                    
                    Console.WriteLine($"✅ API call successful for {model}");
                    Console.WriteLine($"Updated metrics - Avg Response: {service.AverageResponseTimeMs:F1}ms, Success Rate: {service.SuccessRate:F1}%");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"❌ API call failed: {errorContent}");
                    
                    service.UpdatePerformanceMetrics(duration, false);
                    service.UpdateHealth(false, $"Failed to call {model}: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Exception occurred: {ex.Message}");
                service.UpdateHealth(false, $"Exception calling {model}: {ex.Message}");
            }
        }
    }
}
