using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace AITalk.Test
{
    // 简化的数据模型用于测试
    public class SimpleServiceData
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string ServiceName { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = string.Empty;
        public string PrimaryApiKey { get; set; } = string.Empty;
        public string PrimaryIpAddress { get; set; } = string.Empty;
        public int ServerPort { get; set; } = 443;
        public bool UseSSL { get; set; } = true;
        public double HealthScore { get; set; } = 100.0;
        public bool IsHealthy { get; set; } = true;
        public double AverageResponseTimeMs { get; set; } = 0.0;
        public double SuccessRate { get; set; } = 100.0;
        public long TotalRequests { get; set; } = 0;
        public long SuccessfulRequests { get; set; } = 0;
        public long FailedRequests { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        public void UpdatePerformanceMetrics(TimeSpan responseTime, bool isSuccess)
        {
            TotalRequests++;

            if (isSuccess)
            {
                SuccessfulRequests++;
                SuccessRate = (double)SuccessfulRequests / TotalRequests * 100.0;
            }
            else
            {
                FailedRequests++;
                SuccessRate = (double)SuccessfulRequests / TotalRequests * 100.0;
            }

            // 简单的移动平均
            AverageResponseTimeMs = (AverageResponseTimeMs * 0.9) + (responseTime.TotalMilliseconds * 0.1);
            LastUpdated = DateTime.UtcNow;
        }

        public void UpdateHealth(bool isHealthy, string message = "")
        {
            IsHealthy = isHealthy;
            if (isHealthy)
            {
                HealthScore = Math.Min(100.0, HealthScore + 5.0);
            }
            else
            {
                HealthScore = Math.Max(0.0, HealthScore - 10.0);
            }
            LastUpdated = DateTime.UtcNow;
        }
    }

    public class SimpleConversationData
    {
        public Guid ConversationId { get; set; } = Guid.NewGuid();
        public string ConversationName { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public int MaxRounds { get; set; } = 10;
        public int MaxLoopIterations { get; set; } = 5;
        public int MaxConcurrentTasks { get; set; } = 3;
        public List<string> Messages { get; set; } = new();
        public List<string> Tasks { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        public void AddMessage(string message)
        {
            Messages.Add(message);
            LastUpdated = DateTime.UtcNow;
        }

        public void AddTask(string task)
        {
            Tasks.Add(task);
            LastUpdated = DateTime.UtcNow;
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== AITalk New Architecture Test ===");
            Console.WriteLine("Testing the redesigned data structures according to your requirements");
            Console.WriteLine();

            // 测试新的服务数据结构
            await TestServiceData();
            Console.WriteLine();

            // 测试新的对话数据结构
            await TestConversationData();
            Console.WriteLine();

            // 测试真实的DeepSeek API调用
            await TestRealDeepSeekAPI();
            Console.WriteLine();

            Console.WriteLine("All tests completed successfully! ✅");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static async Task TestServiceData()
        {
            Console.WriteLine("--- Testing SimpleServiceData (New Architecture) ---");

            // 创建DeepSeek服务配置 - 展示新的详细数据结构
            var serviceData = new SimpleServiceData
            {
                ServiceName = "DeepSeek API Service",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22",
                PrimaryIpAddress = "************",
                ServerPort = 443,
                UseSSL = true,
                IsHealthy = true,
                HealthScore = 100.0
            };

            Console.WriteLine($"✅ Service Created: {serviceData.ServiceName}");
            Console.WriteLine($"   ID: {serviceData.Id}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   Primary IP: {serviceData.PrimaryIpAddress}:{serviceData.ServerPort}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey[..10]}...");
            Console.WriteLine($"   Health Score: {serviceData.HealthScore}%");
            Console.WriteLine($"   SSL Enabled: {serviceData.UseSSL}");

            // 测试性能指标更新 - 展示新架构的功能
            Console.WriteLine("\n   Testing performance metrics update:");
            serviceData.UpdatePerformanceMetrics(TimeSpan.FromMilliseconds(1500), true);
            Console.WriteLine($"   ✓ Updated metrics - Response Time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   ✓ Success Rate: {serviceData.SuccessRate:F1}%");
            Console.WriteLine($"   ✓ Total Requests: {serviceData.TotalRequests}");

            // 测试健康状态更新
            Console.WriteLine("\n   Testing health status update:");
            serviceData.UpdateHealth(true, "Service is healthy and ready");
            Console.WriteLine($"   ✓ Health Status: {(serviceData.IsHealthy ? "Healthy" : "Unhealthy")}");
            Console.WriteLine($"   ✓ Health Score: {serviceData.HealthScore}%");

            await Task.CompletedTask;
        }

        static async Task TestConversationData()
        {
            Console.WriteLine("--- Testing SimpleConversationData (New Architecture) ---");

            // 创建对话数据 - 展示新的多轮调度和自循环控制架构
            var conversationData = new SimpleConversationData
            {
                ConversationName = "AI Assistant Multi-Round Conversation",
                UserId = "user123",
                MaxRounds = 10,
                MaxLoopIterations = 5,
                MaxConcurrentTasks = 3
            };

            Console.WriteLine($"✅ Conversation Created: {conversationData.ConversationName}");
            Console.WriteLine($"   ID: {conversationData.ConversationId}");
            Console.WriteLine($"   User: {conversationData.UserId}");
            Console.WriteLine($"   Max Rounds: {conversationData.MaxRounds}");
            Console.WriteLine($"   Max Loop Iterations: {conversationData.MaxLoopIterations}");
            Console.WriteLine($"   Max Concurrent Tasks: {conversationData.MaxConcurrentTasks}");

            // 测试消息添加 - 展示新架构的消息管理
            Console.WriteLine("\n   Testing message management:");
            conversationData.AddMessage("User: Hello, I need help with planning a trip to Japan.");
            Console.WriteLine($"   ✓ Added user message");

            conversationData.AddMessage("Assistant: I'd be happy to help you plan your trip to Japan!");
            Console.WriteLine($"   ✓ Added assistant message");

            // 测试任务添加 - 展示新架构的多轮任务调度
            Console.WriteLine("\n   Testing multi-round task scheduling:");
            conversationData.AddTask("Task 1: Analyze User Request (Round 1)");
            Console.WriteLine($"   ✓ Added analysis task");

            conversationData.AddTask("Task 2: Gather Information (Round 1)");
            Console.WriteLine($"   ✓ Added information gathering task");

            conversationData.AddTask("Task 3: Generate Recommendations (Round 2)");
            Console.WriteLine($"   ✓ Added recommendation task");

            // 展示数据统计
            Console.WriteLine("\n   Current conversation statistics:");
            Console.WriteLine($"   ✓ Total messages: {conversationData.Messages.Count}");
            Console.WriteLine($"   ✓ Total tasks: {conversationData.Tasks.Count}");
            Console.WriteLine($"   ✓ Created at: {conversationData.CreatedAt:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"   ✓ Last updated: {conversationData.LastUpdated:yyyy-MM-dd HH:mm:ss}");

            await Task.CompletedTask;
        }

        static async Task TestRealDeepSeekAPI()
        {
            Console.WriteLine("--- Testing Real DeepSeek API ---");
            Console.WriteLine("This demonstrates the new architecture with actual API calls");

            var serviceData = new SimpleServiceData
            {
                ServiceName = "DeepSeek API",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22"
            };

            Console.WriteLine($"✅ Testing with service: {serviceData.ServiceName}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey[..10]}...");

            // 测试deepseek-chat模型
            Console.WriteLine("\n   Testing deepseek-chat model:");
            await TestModel(serviceData, "deepseek-chat", "你好，请简单介绍一下你自己。");

            Console.WriteLine("\n   Testing deepseek-reasoner model:");
            await TestModel(serviceData, "deepseek-reasoner", "请解释一下什么是人工智能。");

            // 展示服务性能统计
            Console.WriteLine("\n   Final service statistics:");
            Console.WriteLine($"   ✓ Total requests: {serviceData.TotalRequests}");
            Console.WriteLine($"   ✓ Successful requests: {serviceData.SuccessfulRequests}");
            Console.WriteLine($"   ✓ Failed requests: {serviceData.FailedRequests}");
            Console.WriteLine($"   ✓ Success rate: {serviceData.SuccessRate:F1}%");
            Console.WriteLine($"   ✓ Average response time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   ✓ Health score: {serviceData.HealthScore:F1}%");
        }

        static async Task TestModel(SimpleServiceData service, string model, string prompt)
        {
            Console.WriteLine($"Testing model: {model}");
            Console.WriteLine($"Prompt: {prompt}");

            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {service.PrimaryApiKey}");

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 500,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var startTime = DateTime.UtcNow;
                var response = await httpClient.PostAsync($"{service.BaseUrl}/chat/completions", content);
                var duration = DateTime.UtcNow - startTime;

                Console.WriteLine($"Response Status: {response.StatusCode}");
                Console.WriteLine($"Response Time: {duration.TotalMilliseconds:F0}ms");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var responseJson = JsonDocument.Parse(responseContent);
                    
                    if (responseJson.RootElement.TryGetProperty("choices", out var choices) && 
                        choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var messageContent))
                        {
                            Console.WriteLine($"Response: {messageContent.GetString()}");
                        }
                    }

                    // 更新服务性能指标
                    service.UpdatePerformanceMetrics(duration, true);
                    service.UpdateHealth(true, $"Successfully called {model}");
                    
                    Console.WriteLine($"✅ API call successful for {model}");
                    Console.WriteLine($"Updated metrics - Avg Response: {service.AverageResponseTimeMs:F1}ms, Success Rate: {service.SuccessRate:F1}%");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"❌ API call failed: {errorContent}");
                    
                    service.UpdatePerformanceMetrics(duration, false);
                    service.UpdateHealth(false, $"Failed to call {model}: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Exception occurred: {ex.Message}");
                service.UpdateHealth(false, $"Exception calling {model}: {ex.Message}");
            }
        }
    }
}
