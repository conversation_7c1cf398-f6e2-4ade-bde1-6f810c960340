using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace AITalk.Test
{
    // 简化的数据模型用于测试
    public class SimpleServiceData
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string ServiceName { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = string.Empty;
        public string PrimaryApiKey { get; set; } = string.Empty;
        public string PrimaryIpAddress { get; set; } = string.Empty;
        public int ServerPort { get; set; } = 443;
        public bool UseSSL { get; set; } = true;
        public double HealthScore { get; set; } = 100.0;
        public bool IsHealthy { get; set; } = true;
        public double AverageResponseTimeMs { get; set; } = 0.0;
        public double SuccessRate { get; set; } = 100.0;
        public long TotalRequests { get; set; } = 0;
        public long SuccessfulRequests { get; set; } = 0;
        public long FailedRequests { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        public void UpdatePerformanceMetrics(TimeSpan responseTime, bool isSuccess)
        {
            TotalRequests++;

            if (isSuccess)
            {
                SuccessfulRequests++;
                SuccessRate = (double)SuccessfulRequests / TotalRequests * 100.0;
            }
            else
            {
                FailedRequests++;
                SuccessRate = (double)SuccessfulRequests / TotalRequests * 100.0;
            }

            // 简单的移动平均
            AverageResponseTimeMs = (AverageResponseTimeMs * 0.9) + (responseTime.TotalMilliseconds * 0.1);
            LastUpdated = DateTime.UtcNow;
        }

        public void UpdateHealth(bool isHealthy, string message = "")
        {
            IsHealthy = isHealthy;
            if (isHealthy)
            {
                HealthScore = Math.Min(100.0, HealthScore + 5.0);
            }
            else
            {
                HealthScore = Math.Max(0.0, HealthScore - 10.0);
            }
            LastUpdated = DateTime.UtcNow;
        }
    }

    public enum AITalkStatus
    {
        Created,
        Running,
        Completed,
        Failed,
        Cancelled
    }

    public class SimpleConversationData
    {
        public Guid ConversationId { get; set; } = Guid.NewGuid();
        public string ConversationName { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public int MaxRounds { get; set; } = 10;
        public int MaxLoopIterations { get; set; } = 5;
        public int MaxConcurrentTasks { get; set; } = 3;
        public List<string> Messages { get; set; } = new();
        public List<string> Tasks { get; set; } = new();

        // === AITalk核心：子任务管理 ===
        /// <summary>
        /// 子AITalk列表 - 这是AITalk的核心功能
        /// </summary>
        public List<SimpleConversationData> SubAITalks { get; set; } = new();

        /// <summary>
        /// 父AITalk引用
        /// </summary>
        public SimpleConversationData? ParentAITalk { get; set; }

        /// <summary>
        /// AITalk状态
        /// </summary>
        public AITalkStatus Status { get; set; } = AITalkStatus.Created;

        /// <summary>
        /// 当前正在执行的子任务数量
        /// </summary>
        public int RunningSubTasks { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }

        public void AddMessage(string message)
        {
            Messages.Add(message);
            LastUpdated = DateTime.UtcNow;
        }

        public void AddTask(string task)
        {
            Tasks.Add(task);
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 添加子AITalk任务
        /// </summary>
        public void AddSubAITalk(SimpleConversationData subAITalk)
        {
            subAITalk.ParentAITalk = this;
            SubAITalks.Add(subAITalk);
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 开始执行AITalk
        /// </summary>
        public void Start()
        {
            Status = AITalkStatus.Running;
            StartedAt = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 完成AITalk
        /// </summary>
        public void Complete()
        {
            Status = AITalkStatus.Completed;
            CompletedAt = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;

            // 通知父任务检查状态
            ParentAITalk?.CheckSubTasksStatus();
        }

        /// <summary>
        /// 失败AITalk
        /// </summary>
        public void Fail()
        {
            Status = AITalkStatus.Failed;
            CompletedAt = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;

            // 通知父任务检查状态
            ParentAITalk?.CheckSubTasksStatus();
        }

        /// <summary>
        /// 检查子任务状态
        /// </summary>
        public void CheckSubTasksStatus()
        {
            RunningSubTasks = SubAITalks.Count(t => t.Status == AITalkStatus.Running);

            var completedTasks = SubAITalks.Count(t => t.Status == AITalkStatus.Completed);
            var failedTasks = SubAITalks.Count(t => t.Status == AITalkStatus.Failed);
            var totalTasks = SubAITalks.Count;

            // 如果所有子任务都完成了，标记自己为完成
            if (totalTasks > 0 && (completedTasks + failedTasks) == totalTasks)
            {
                if (failedTasks == 0)
                {
                    Complete();
                }
                else
                {
                    Fail();
                }
            }
        }

        /// <summary>
        /// 获取所有子任务的状态统计
        /// </summary>
        public (int created, int running, int completed, int failed, int cancelled) GetSubTasksStats()
        {
            return (
                SubAITalks.Count(t => t.Status == AITalkStatus.Created),
                SubAITalks.Count(t => t.Status == AITalkStatus.Running),
                SubAITalks.Count(t => t.Status == AITalkStatus.Completed),
                SubAITalks.Count(t => t.Status == AITalkStatus.Failed),
                SubAITalks.Count(t => t.Status == AITalkStatus.Cancelled)
            );
        }

        /// <summary>
        /// 检查是否所有子任务都已完成
        /// </summary>
        public bool AreAllSubTasksCompleted()
        {
            if (SubAITalks.Count == 0) return true;
            return SubAITalks.All(t => t.Status == AITalkStatus.Completed || t.Status == AITalkStatus.Failed);
        }

        /// <summary>
        /// 获取执行时间
        /// </summary>
        public TimeSpan GetExecutionTime()
        {
            if (StartedAt == null) return TimeSpan.Zero;
            var endTime = CompletedAt ?? DateTime.UtcNow;
            return endTime - StartedAt.Value;
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== AITalk Multi-Round Conversation & Concurrency Test ===");
            Console.WriteLine("Testing multi-round conversation with concurrent task processing");
            Console.WriteLine();

            // 测试多轮对话和并发处理
            await TestMultiRoundConversationWithConcurrency();
            Console.WriteLine();

            Console.WriteLine("All tests completed successfully! ✅");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static async Task TestMultiRoundConversationWithConcurrency()
        {
            Console.WriteLine("🚀 Starting AITalk Multi-Round Test with SubAITalk Management");

            // 创建DeepSeek服务
            var service = new SimpleServiceData
            {
                ServiceName = "DeepSeek API",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22"
            };

            // 创建主AITalk
            var mainAITalk = new SimpleConversationData
            {
                ConversationName = "Main AITalk - Multi-Round Conversation",
                UserId = "test_user",
                MaxConcurrentTasks = 3
            };

            Console.WriteLine($"✅ Created main AITalk: {mainAITalk.ConversationName}");
            Console.WriteLine($"   ID: {mainAITalk.ConversationId}");
            Console.WriteLine($"   Status: {mainAITalk.Status}");
            Console.WriteLine($"   Max concurrent tasks: {mainAITalk.MaxConcurrentTasks}");
            Console.WriteLine();

            // 开始主AITalk
            mainAITalk.Start();
            Console.WriteLine($"🔄 Main AITalk started - Status: {mainAITalk.Status}");

            // 第一轮：问一个问题
            Console.WriteLine("\n📝 Round 1: Asking initial question to main AITalk");
            var initialQuestion = "请详细介绍一下人工智能的发展历史和主要应用领域。";
            mainAITalk.AddMessage($"User: {initialQuestion}");

            var initialResponse = await CallDeepSeekAPI(service, "deepseek-chat", initialQuestion);
            mainAITalk.AddMessage($"Assistant: {initialResponse}");

            Console.WriteLine($"✅ Main AITalk got response ({initialResponse.Length} characters)");
            Console.WriteLine();

            // 第二轮：创建子AITalk任务并添加到主AITalk的SubAITalks列表中
            Console.WriteLine("🔄 Round 2: Creating SubAITalks and adding to main AITalk");

            // 创建MD格式转换子任务
            var mdSubAITalk = new SimpleConversationData
            {
                ConversationName = "SubAITalk: Convert to Markdown",
                UserId = "test_user"
            };
            mdSubAITalk.AddMessage($"User: 请将以下内容转换为Markdown格式：\n\n{initialResponse}");
            mainAITalk.AddSubAITalk(mdSubAITalk);

            // 创建HTML格式转换子任务
            var htmlSubAITalk = new SimpleConversationData
            {
                ConversationName = "SubAITalk: Convert to HTML",
                UserId = "test_user"
            };
            htmlSubAITalk.AddMessage($"User: 请将以下内容转换为HTML格式：\n\n{initialResponse}");
            mainAITalk.AddSubAITalk(htmlSubAITalk);

            // 创建总结子任务
            var summarySubAITalk = new SimpleConversationData
            {
                ConversationName = "SubAITalk: Create Summary",
                UserId = "test_user"
            };
            summarySubAITalk.AddMessage($"User: 请为以下内容创建一个简洁的摘要：\n\n{initialResponse}");
            mainAITalk.AddSubAITalk(summarySubAITalk);

            Console.WriteLine($"✅ Added {mainAITalk.SubAITalks.Count} SubAITalks to main AITalk:");
            foreach (var subAITalk in mainAITalk.SubAITalks)
            {
                Console.WriteLine($"   - {subAITalk.ConversationName} (ID: {subAITalk.ConversationId})");
                Console.WriteLine($"     Status: {subAITalk.Status}, Parent: {subAITalk.ParentAITalk?.ConversationName}");
            }
            Console.WriteLine();

            // 并发执行所有子AITalk任务
            Console.WriteLine("⚡ Starting concurrent execution of all SubAITalks...");
            var startTime = DateTime.UtcNow;

            // 为每个子AITalk创建并发任务
            var concurrentTasks = new List<Task>();
            for (int i = 0; i < mainAITalk.SubAITalks.Count; i++)
            {
                var taskIndex = i;
                var subAITalk = mainAITalk.SubAITalks[taskIndex];

                var task = Task.Run(async () =>
                {
                    Console.WriteLine($"🔄 SubAITalk {taskIndex + 1} ({subAITalk.ConversationName}) started");
                    subAITalk.Start();

                    try
                    {
                        var lastMessage = subAITalk.Messages.Last();
                        var prompt = lastMessage.Replace("User: ", "");

                        var response = await CallDeepSeekAPI(service, "deepseek-chat", prompt);
                        subAITalk.AddMessage($"Assistant: {response}");

                        // 标记子任务完成
                        subAITalk.Complete();

                        var duration = subAITalk.GetExecutionTime();
                        Console.WriteLine($"✅ SubAITalk {taskIndex + 1} ({subAITalk.ConversationName}) completed in {duration.TotalSeconds:F1}s");
                        Console.WriteLine($"   Status: {subAITalk.Status}");

                        return new { TaskIndex = taskIndex + 1, Name = subAITalk.ConversationName, Response = response, Duration = duration };
                    }
                    catch (Exception ex)
                    {
                        subAITalk.Fail();
                        var duration = subAITalk.GetExecutionTime();
                        Console.WriteLine($"❌ SubAITalk {taskIndex + 1} ({subAITalk.ConversationName}) failed in {duration.TotalSeconds:F1}s: {ex.Message}");
                        Console.WriteLine($"   Status: {subAITalk.Status}");
                        return new { TaskIndex = taskIndex + 1, Name = subAITalk.ConversationName, Response = $"Error: {ex.Message}", Duration = duration };
                    }
                });

                concurrentTasks.Add(task);
            }

            // 等待所有子任务完成
            await Task.WhenAll(concurrentTasks);
            var totalDuration = DateTime.UtcNow - startTime;

            Console.WriteLine();
            Console.WriteLine($"🎯 All SubAITalks completed in {totalDuration.TotalSeconds:F1}s");

            // 检查主AITalk状态
            Console.WriteLine($"📊 Main AITalk Status: {mainAITalk.Status}");
            Console.WriteLine($"   Execution Time: {mainAITalk.GetExecutionTime().TotalSeconds:F1}s");
            Console.WriteLine($"   All SubTasks Completed: {mainAITalk.AreAllSubTasksCompleted()}");

            var stats = mainAITalk.GetSubTasksStats();
            Console.WriteLine($"   SubTasks Stats - Created: {stats.created}, Running: {stats.running}, Completed: {stats.completed}, Failed: {stats.failed}");
            Console.WriteLine();

            // 显示每个子AITalk的结果
            Console.WriteLine("� SubAITalk Results:");
            for (int i = 0; i < mainAITalk.SubAITalks.Count; i++)
            {
                var subAITalk = mainAITalk.SubAITalks[i];
                var lastMessage = subAITalk.Messages.Last();
                var responseLength = lastMessage.Length;

                Console.WriteLine($"   SubAITalk {i + 1}: {subAITalk.ConversationName}");
                Console.WriteLine($"   - ID: {subAITalk.ConversationId}");
                Console.WriteLine($"   - Status: {subAITalk.Status}");
                Console.WriteLine($"   - Messages: {subAITalk.Messages.Count}");
                Console.WriteLine($"   - Response length: {responseLength} characters");
                Console.WriteLine($"   - Execution time: {subAITalk.GetExecutionTime().TotalSeconds:F1}s");
                Console.WriteLine($"   - Preview: {lastMessage[..Math.Min(100, responseLength)]}...");
                Console.WriteLine();
            }

            // 显示性能统计
            Console.WriteLine("📈 Performance Statistics:");
            Console.WriteLine($"   - Total requests: {service.TotalRequests}");
            Console.WriteLine($"   - Success rate: {service.SuccessRate:F1}%");
            Console.WriteLine($"   - Average response time: {service.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   - Main AITalk SubTasks: {mainAITalk.SubAITalks.Count}");
            Console.WriteLine($"   - Total execution time: {totalDuration.TotalSeconds:F1}s");
            Console.WriteLine($"   - Concurrency efficiency: {(mainAITalk.SubAITalks.Count * service.AverageResponseTimeMs / 1000.0 / totalDuration.TotalSeconds):F1}x");
        }

        static async Task<string> CallDeepSeekAPI(SimpleServiceData service, string model, string prompt)
        {
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {service.PrimaryApiKey}");

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 2000,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var startTime = DateTime.UtcNow;
                var response = await httpClient.PostAsync($"{service.BaseUrl}/chat/completions", content);
                var duration = DateTime.UtcNow - startTime;

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var responseJson = JsonDocument.Parse(responseContent);

                    if (responseJson.RootElement.TryGetProperty("choices", out var choices) &&
                        choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var messageContent))
                        {
                            // 更新服务性能指标
                            service.UpdatePerformanceMetrics(duration, true);
                            service.UpdateHealth(true, $"Successfully called {model}");

                            return messageContent.GetString() ?? "Empty response";
                        }
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    service.UpdatePerformanceMetrics(duration, false);
                    service.UpdateHealth(false, $"Failed to call {model}: {response.StatusCode}");
                    return $"API Error: {response.StatusCode} - {errorContent}";
                }
            }
            catch (Exception ex)
            {
                service.UpdateHealth(false, $"Exception calling {model}: {ex.Message}");
                return $"Exception: {ex.Message}";
            }

            return "No response received";
        }

        static async Task TestServiceData()
        {
            Console.WriteLine("--- Testing SimpleServiceData (New Architecture) ---");

            // 创建DeepSeek服务配置 - 展示新的详细数据结构
            var serviceData = new SimpleServiceData
            {
                ServiceName = "DeepSeek API Service",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22",
                PrimaryIpAddress = "************",
                ServerPort = 443,
                UseSSL = true,
                IsHealthy = true,
                HealthScore = 100.0
            };

            Console.WriteLine($"✅ Service Created: {serviceData.ServiceName}");
            Console.WriteLine($"   ID: {serviceData.Id}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   Primary IP: {serviceData.PrimaryIpAddress}:{serviceData.ServerPort}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey[..10]}...");
            Console.WriteLine($"   Health Score: {serviceData.HealthScore}%");
            Console.WriteLine($"   SSL Enabled: {serviceData.UseSSL}");

            // 测试性能指标更新 - 展示新架构的功能
            Console.WriteLine("\n   Testing performance metrics update:");
            serviceData.UpdatePerformanceMetrics(TimeSpan.FromMilliseconds(1500), true);
            Console.WriteLine($"   ✓ Updated metrics - Response Time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   ✓ Success Rate: {serviceData.SuccessRate:F1}%");
            Console.WriteLine($"   ✓ Total Requests: {serviceData.TotalRequests}");

            // 测试健康状态更新
            Console.WriteLine("\n   Testing health status update:");
            serviceData.UpdateHealth(true, "Service is healthy and ready");
            Console.WriteLine($"   ✓ Health Status: {(serviceData.IsHealthy ? "Healthy" : "Unhealthy")}");
            Console.WriteLine($"   ✓ Health Score: {serviceData.HealthScore}%");

            await Task.CompletedTask;
        }

        static async Task TestConversationData()
        {
            Console.WriteLine("--- Testing SimpleConversationData (New Architecture) ---");

            // 创建对话数据 - 展示新的多轮调度和自循环控制架构
            var conversationData = new SimpleConversationData
            {
                ConversationName = "AI Assistant Multi-Round Conversation",
                UserId = "user123",
                MaxRounds = 10,
                MaxLoopIterations = 5,
                MaxConcurrentTasks = 3
            };

            Console.WriteLine($"✅ Conversation Created: {conversationData.ConversationName}");
            Console.WriteLine($"   ID: {conversationData.ConversationId}");
            Console.WriteLine($"   User: {conversationData.UserId}");
            Console.WriteLine($"   Max Rounds: {conversationData.MaxRounds}");
            Console.WriteLine($"   Max Loop Iterations: {conversationData.MaxLoopIterations}");
            Console.WriteLine($"   Max Concurrent Tasks: {conversationData.MaxConcurrentTasks}");

            // 测试消息添加 - 展示新架构的消息管理
            Console.WriteLine("\n   Testing message management:");
            conversationData.AddMessage("User: Hello, I need help with planning a trip to Japan.");
            Console.WriteLine($"   ✓ Added user message");

            conversationData.AddMessage("Assistant: I'd be happy to help you plan your trip to Japan!");
            Console.WriteLine($"   ✓ Added assistant message");

            // 测试任务添加 - 展示新架构的多轮任务调度
            Console.WriteLine("\n   Testing multi-round task scheduling:");
            conversationData.AddTask("Task 1: Analyze User Request (Round 1)");
            Console.WriteLine($"   ✓ Added analysis task");

            conversationData.AddTask("Task 2: Gather Information (Round 1)");
            Console.WriteLine($"   ✓ Added information gathering task");

            conversationData.AddTask("Task 3: Generate Recommendations (Round 2)");
            Console.WriteLine($"   ✓ Added recommendation task");

            // 展示数据统计
            Console.WriteLine("\n   Current conversation statistics:");
            Console.WriteLine($"   ✓ Total messages: {conversationData.Messages.Count}");
            Console.WriteLine($"   ✓ Total tasks: {conversationData.Tasks.Count}");
            Console.WriteLine($"   ✓ Created at: {conversationData.CreatedAt:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"   ✓ Last updated: {conversationData.LastUpdated:yyyy-MM-dd HH:mm:ss}");

            await Task.CompletedTask;
        }

        static async Task TestRealDeepSeekAPI()
        {
            Console.WriteLine("--- Testing Real DeepSeek API ---");
            Console.WriteLine("This demonstrates the new architecture with actual API calls");

            var serviceData = new SimpleServiceData
            {
                ServiceName = "DeepSeek API",
                BaseUrl = "https://api.deepseek.com/v1",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22"
            };

            Console.WriteLine($"✅ Testing with service: {serviceData.ServiceName}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey[..10]}...");

            // 测试deepseek-chat模型
            Console.WriteLine("\n   Testing deepseek-chat model:");
            await TestModel(serviceData, "deepseek-chat", "你好，请简单介绍一下你自己。");

            Console.WriteLine("\n   Testing deepseek-reasoner model:");
            await TestModel(serviceData, "deepseek-reasoner", "请解释一下什么是人工智能。");

            // 展示服务性能统计
            Console.WriteLine("\n   Final service statistics:");
            Console.WriteLine($"   ✓ Total requests: {serviceData.TotalRequests}");
            Console.WriteLine($"   ✓ Successful requests: {serviceData.SuccessfulRequests}");
            Console.WriteLine($"   ✓ Failed requests: {serviceData.FailedRequests}");
            Console.WriteLine($"   ✓ Success rate: {serviceData.SuccessRate:F1}%");
            Console.WriteLine($"   ✓ Average response time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   ✓ Health score: {serviceData.HealthScore:F1}%");
        }

        static async Task TestModel(SimpleServiceData service, string model, string prompt)
        {
            Console.WriteLine($"Testing model: {model}");
            Console.WriteLine($"Prompt: {prompt}");

            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {service.PrimaryApiKey}");

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 500,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var startTime = DateTime.UtcNow;
                var response = await httpClient.PostAsync($"{service.BaseUrl}/chat/completions", content);
                var duration = DateTime.UtcNow - startTime;

                Console.WriteLine($"Response Status: {response.StatusCode}");
                Console.WriteLine($"Response Time: {duration.TotalMilliseconds:F0}ms");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var responseJson = JsonDocument.Parse(responseContent);
                    
                    if (responseJson.RootElement.TryGetProperty("choices", out var choices) && 
                        choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var messageContent))
                        {
                            Console.WriteLine($"Response: {messageContent.GetString()}");
                        }
                    }

                    // 更新服务性能指标
                    service.UpdatePerformanceMetrics(duration, true);
                    service.UpdateHealth(true, $"Successfully called {model}");
                    
                    Console.WriteLine($"✅ API call successful for {model}");
                    Console.WriteLine($"Updated metrics - Avg Response: {service.AverageResponseTimeMs:F1}ms, Success Rate: {service.SuccessRate:F1}%");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"❌ API call failed: {errorContent}");
                    
                    service.UpdatePerformanceMetrics(duration, false);
                    service.UpdateHealth(false, $"Failed to call {model}: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Exception occurred: {ex.Message}");
                service.UpdateHealth(false, $"Exception calling {model}: {ex.Message}");
            }
        }
    }
}
