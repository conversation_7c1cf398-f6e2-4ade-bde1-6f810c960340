using System;

namespace AITalk.Core.Enums
{
    /// <summary>
    /// OpenAI兼容提供商类型枚举
    /// </summary>
    public enum ProviderType
    {
        OpenAI,
        Azure,
        Anthropic,
        Google,
        Cohere,
        HuggingFace,
        LocalLLM,
        CustomProvider,
        Ollama,
        LMStudio,
        TextGenWebUI,
        FastChat,
        vLLM,
        TGI
    }

    /// <summary>
    /// 兼容性级别枚举
    /// </summary>
    public enum CompatibilityLevel
    {
        Full,
        High,
        Medium,
        Low,
        Minimal,
        None
    }

    /// <summary>
    /// 认证方法枚举
    /// </summary>
    public enum AuthMethod
    {
        ApiKey,
        BearerToken,
        OAuth2,
        Basic,
        Custom,
        None
    }

    /// <summary>
    /// 模型类型枚举
    /// </summary>
    public enum ModelType
    {
        ChatCompletion,
        TextCompletion,
        Embedding,
        ImageGeneration,
        AudioTranscription,
        AudioTranslation,
        Moderation,
        FineTuned,
        Custom
    }

    /// <summary>
    /// 标准错误代码枚举
    /// </summary>
    public enum StandardErrorCode
    {
        InvalidRequest,
        AuthenticationFailed,
        PermissionDenied,
        NotFound,
        RateLimitExceeded,
        QuotaExceeded,
        ServerError,
        ServiceUnavailable,
        Timeout,
        InvalidModel,
        ContentFiltered,
        TokenLimitExceeded,
        InsufficientQuota,
        ModelOverloaded,
        BadGateway,
        Unknown
    }

    /// <summary>
    /// 重试策略枚举
    /// </summary>
    public enum RetryStrategyType
    {
        None,
        Immediate,
        Fixed,
        Linear,
        Exponential,
        Custom
    }

    /// <summary>
    /// 速率限制动作枚举
    /// </summary>
    public enum RateLimitAction
    {
        Wait,
        SwitchProvider,
        Queue,
        Reject,
        Retry
    }

    /// <summary>
    /// 认证恢复动作枚举
    /// </summary>
    public enum AuthRecoveryAction
    {
        RefreshToken,
        ReAuthenticate,
        SwitchCredentials,
        Manual,
        Fail
    }

    /// <summary>
    /// 配额恢复动作枚举
    /// </summary>
    public enum QuotaRecoveryAction
    {
        Wait,
        SwitchProvider,
        UpgradeQuota,
        UseBackup,
        Fail
    }

    /// <summary>
    /// 本地LLM类型枚举
    /// </summary>
    public enum LocalLLMType
    {
        Ollama,
        LMStudio,
        TextGenWebUI,
        FastChat,
        vLLM,
        TGI,
        Kobold,
        GPT4All,
        LlamaCpp,
        Custom
    }

    /// <summary>
    /// 提供商健康状态枚举
    /// </summary>
    public enum ProviderHealthStatus
    {
        Healthy,
        Degraded,
        Unhealthy,
        Unknown,
        Maintenance
    }

    /// <summary>
    /// 流式响应状态枚举
    /// </summary>
    public enum StreamingStatus
    {
        Starting,
        Streaming,
        Completed,
        Error,
        Cancelled,
        Timeout
    }

    /// <summary>
    /// 端点类型枚举
    /// </summary>
    public enum EndpointType
    {
        ChatCompletion,
        Completion,
        Embedding,
        Models,
        ImageGeneration,
        AudioTranscription,
        AudioTranslation,
        Moderation,
        FineTuning,
        Custom
    }

    /// <summary>
    /// 导出格式枚举
    /// </summary>
    public enum ExportFormat
    {
        Json,
        Csv,
        Excel,
        Xml,
        Yaml,
        Text
    }

    /// <summary>
    /// 缓存策略枚举
    /// </summary>
    public enum CacheStrategy
    {
        None,
        Memory,
        Disk,
        Redis,
        Database,
        Hybrid
    }
}
