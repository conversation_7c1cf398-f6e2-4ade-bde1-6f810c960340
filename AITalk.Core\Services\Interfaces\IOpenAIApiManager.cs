using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AITalk.Core.Enums;
using AITalk.Core.Models;
using AITalk.Core.Models.OpenAI;

namespace AITalk.Core.Services.Interfaces
{
    /// <summary>
    /// OpenAI API管理器接口
    /// </summary>
    public interface IOpenAIApiManager : IDisposable
    {
        /// <summary>
        /// 注册OpenAI兼容提供商
        /// </summary>
        Task<bool> RegisterProviderAsync(OpenAICompatibleProvider provider);

        /// <summary>
        /// 注销提供商
        /// </summary>
        Task<bool> UnregisterProviderAsync(ProviderType providerType);

        /// <summary>
        /// 获取可用提供商
        /// </summary>
        Task<List<ProviderType>> GetAvailableProvidersAsync();

        /// <summary>
        /// 发送聊天完成请求
        /// </summary>
        Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request, ProviderType? preferredProvider = null);

        /// <summary>
        /// 发送流式聊天完成请求
        /// </summary>
        IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request, ProviderType? preferredProvider = null);

        /// <summary>
        /// 获取可用模型
        /// </summary>
        Task<List<ModelInfo>> GetAvailableModelsAsync(ProviderType? providerType = null);

        /// <summary>
        /// 获取模型信息
        /// </summary>
        Task<ModelInfo?> GetModelInfoAsync(string modelName, ProviderType? providerType = null);
    }

    /// <summary>
    /// OpenAI兼容提供商接口
    /// </summary>
    public interface IOpenAICompatibleProvider : IDisposable
    {
        ProviderType ProviderType { get; }
        string ProviderName { get; }
        bool SupportsStreaming { get; }
        
        Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request);
        IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request);
        Task<List<ModelInfo>> GetAvailableModelsAsync();
        Task<ProviderHealthStatus> GetHealthStatusAsync();
        Task<PerformanceMetrics> GetPerformanceMetricsAsync();
        Task ConnectAsync();
        Task DisconnectAsync();
    }

    /// <summary>
    /// OpenAI请求路由器接口
    /// </summary>
    public interface IOpenAIRequestRouter
    {
        Task<ChatCompletionRequest> RouteRequestAsync(ChatCompletionRequest request, ProviderType targetProvider);
        Task<ProviderType> SelectOptimalProviderAsync(ChatCompletionRequest request, List<ProviderType> availableProviders);
        Task<Dictionary<ProviderType, List<ChatCompletionRequest>>> LoadBalanceRequestsAsync(List<ChatCompletionRequest> requests, List<ProviderType> providers);
        Task<ProviderType> HandleProviderFailoverAsync(ChatCompletionRequest request, ProviderType failedProvider);
        Task<ProviderHealthStatus> GetProviderHealthAsync(ProviderType provider);
        Task UpdateProviderWeightsAsync(Dictionary<ProviderType, double> weights);
        Task EnableGeographicRoutingAsync(bool enable);
        Task SetCostOptimizedRoutingAsync(bool enable);
    }

    /// <summary>
    /// OpenAI响应标准化器接口
    /// </summary>
    public interface IOpenAIResponseNormalizer
    {
        Task<ChatCompletionResponse> NormalizeChatCompletionResponseAsync(object response, ProviderType source);
        Task<CompletionResponse> NormalizeCompletionResponseAsync(object response, ProviderType source);
        Task<EmbeddingResponse> NormalizeEmbeddingResponseAsync(object response, ProviderType source);
        Task<OpenAIError> NormalizeErrorResponseAsync(object response, ProviderType source);
        Task<Usage> ConvertUsageMetricsAsync(object usage, ProviderType source);
        Task<List<Choice>> StandardizeChoiceFormatAsync(object choices, ProviderType source);
        Task<Dictionary<string, object>> HandleProviderSpecificFieldsAsync(object response, ProviderType source);
        Task<ValidationResult> ValidateResponseIntegrityAsync(object response);
    }

    /// <summary>
    /// 提供商兼容性检查器接口
    /// </summary>
    public interface IProviderCompatibilityChecker
    {
        Task<CompatibilityReport> CheckOpenAICompatibilityAsync(OpenAICompatibleProvider provider);
        Task<ValidationResult> ValidateRequestFormatAsync(ProviderType provider, object request);
        Task<ValidationResult> ValidateResponseFormatAsync(ProviderType provider, object response);
        Task<double> GetCompatibilityScoreAsync(ProviderType provider);
        Task<List<string>> ListIncompatibleFeaturesAsync(ProviderType provider);
        Task<List<Workaround>> GetWorkaroundsAsync(ProviderType provider, string feature);
        Task<ConnectionTestResult> TestProviderConnectionAsync(OpenAICompatibleProvider provider);
        Task<ProviderBenchmark> BenchmarkProviderAsync(OpenAICompatibleProvider provider);
    }

    /// <summary>
    /// 端点映射器接口
    /// </summary>
    public interface IEndpointMapper
    {
        string MapChatCompletionEndpoint(ProviderType provider);
        string MapCompletionEndpoint(ProviderType provider);
        string MapEmbeddingEndpoint(ProviderType provider);
        string MapModelListEndpoint(ProviderType provider);
        string MapImageGenerationEndpoint(ProviderType provider);
        string MapAudioTranscriptionEndpoint(ProviderType provider);
        string MapModerationEndpoint(ProviderType provider);
        string GetCustomEndpoint(ProviderType provider, string endpointType);
        Task<bool> ValidateEndpointAvailabilityAsync(ProviderType provider, string endpoint);
        Task<EndpointDocumentation> GetEndpointDocumentationAsync(ProviderType provider, string endpoint);
    }

    /// <summary>
    /// OpenAI模型管理器接口
    /// </summary>
    public interface IOpenAIModelManager
    {
        Task<List<ModelInfo>> GetAvailableModelsAsync(ProviderType provider);
        Task<ModelCapabilities> GetModelCapabilitiesAsync(string modelName, ProviderType provider);
        Task<ModelPricing> GetModelPricingAsync(string modelName, ProviderType provider);
        Task<ModelLimits> GetModelLimitsAsync(string modelName, ProviderType provider);
        Task<List<ModelMatch>> FindCompatibleModelAsync(ModelRequirements requirements);
        Task<ModelPerformance> GetModelPerformanceMetricsAsync(string modelName, ProviderType provider);
        Task CacheModelInformationAsync(string modelName, ProviderType provider, ModelInfo info);
        Task RefreshModelCacheAsync(ProviderType provider);
        Task<bool> ValidateModelAvailabilityAsync(string modelName, ProviderType provider);
        Task<List<ModelRecommendation>> GetModelRecommendationsAsync(TaskType taskType);
        Task<ModelInfo?> GetModelInfoAsync(string modelName, ProviderType provider);
    }

    /// <summary>
    /// 流式响应处理器接口
    /// </summary>
    public interface IStreamingResponseHandler
    {
        IAsyncEnumerable<StreamingChunk> HandleStreamingResponseAsync(System.IO.Stream responseStream, ProviderType provider);
        Task<StreamingChunk> ParseStreamingChunkAsync(string chunkData, ProviderType provider);
        Task<StreamingChunk> NormalizeStreamingChunkAsync(StreamingChunk chunk, ProviderType provider);
        Task<StreamingError> HandleStreamingErrorAsync(string errorData, ProviderType provider);
        Task<bool> DetectStreamEndAsync(string chunkData, ProviderType provider);
        Task<ChatCompletionResponse> ReassembleStreamingResponseAsync(List<StreamingChunk> chunks);
        Task HandleStreamingTimeoutAsync(TimeSpan timeout);
        Task<ValidationResult> ValidateStreamingIntegrityAsync(List<StreamingChunk> chunks);
    }

    /// <summary>
    /// OpenAI错误处理器接口
    /// </summary>
    public interface IOpenAIErrorHandler
    {
        Task<OpenAIError> HandleApiErrorAsync(Exception exception, ProviderType provider);
        Task<OpenAIError> ParseErrorResponseAsync(string errorContent, ProviderType provider);
        Task<StandardErrorCode> NormalizeErrorCodeAsync(string errorCode, ProviderType provider);
        Task<RetryStrategyType> GetRetryStrategyAsync(OpenAIError error);
        Task<bool> ShouldRetryRequestAsync(OpenAIError error);
        Task<TimeSpan> CalculateRetryDelayAsync(int attemptNumber, OpenAIError error);
        Task<RetryInstruction> HandleRateLimitErrorAsync(RateLimitError error);
        Task<AuthRecoveryAction> HandleAuthenticationErrorAsync(AuthError error);
        Task<QuotaRecoveryAction> HandleQuotaExceededErrorAsync(QuotaError error);
        Task LogErrorAsync(OpenAIError error, ProviderType provider);
        Task<ErrorReport> GenerateErrorReportAsync(List<OpenAIError> errors, TimeSpan period);
    }

    /// <summary>
    /// 速率限制管理器接口
    /// </summary>
    public interface IRateLimitManager
    {
        Task TrackRequestAsync(ProviderType provider, string endpoint);
        Task<RateLimitStatus> CheckRateLimitAsync(ProviderType provider, string endpoint);
        Task<QuotaInfo> GetRemainingQuotaAsync(ProviderType provider);
        Task<TimeSpan> CalculateOptimalRequestTimingAsync(ProviderType provider);
        Task<RateLimitAction> HandleRateLimitHitAsync(ProviderType provider);
        Task UpdateRateLimitInfoAsync(ProviderType provider, RateLimitHeaders headers);
        Task<DateTime> PredictRateLimitResetAsync(ProviderType provider);
        Task<Dictionary<ProviderType, List<ChatCompletionRequest>>> DistributeRequestsAcrossProvidersAsync(List<ChatCompletionRequest> requests);
        Task SetCustomRateLimitsAsync(ProviderType provider, RateLimitConfiguration config);
        Task<RateLimitStatistics> GetRateLimitStatisticsAsync(ProviderType provider, TimeSpan period);
    }
}
