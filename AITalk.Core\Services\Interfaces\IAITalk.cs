using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AITalk.Core.Models;
using AITalk.Core.Models.ConversationData;

namespace AITalk.Core.Services.Interfaces
{
    /// <summary>
    /// AI对话接口
    /// </summary>
    public interface IAITalk : IDisposable
    {
        /// <summary>
        /// 对话ID
        /// </summary>
        Guid ConversationId { get; }

        /// <summary>
        /// 任务队列
        /// </summary>
        List<AITask> TaskQueue { get; }

        /// <summary>
        /// 当前执行任务
        /// </summary>
        AITask? CurrentTask { get; }

        /// <summary>
        /// 最大轮次限制
        /// </summary>
        int MaxRounds { get; set; }

        /// <summary>
        /// 当前轮次
        /// </summary>
        int CurrentRound { get; }

        /// <summary>
        /// 是否完成
        /// </summary>
        bool IsCompleted { get; }

        /// <summary>
        /// 是否取消
        /// </summary>
        bool IsCancelled { get; }

        /// <summary>
        /// 最终结果
        /// </summary>
        ConversationResult? FinalResult { get; }

        /// <summary>
        /// 关联的数据对象
        /// </summary>
        AITalkData Data { get; }

        /// <summary>
        /// 开始对话
        /// </summary>
        Task<ConversationResult> StartConversationAsync(string initialInput);

        /// <summary>
        /// 处理当前轮次
        /// </summary>
        Task<RoundResult> ProcessCurrentRoundAsync();

        /// <summary>
        /// 执行单个任务
        /// </summary>
        Task<TaskExecutionResult> ExecuteTaskAsync(AITask task);

        /// <summary>
        /// 添加用户消息
        /// </summary>
        Task AddUserMessageAsync(string content);

        /// <summary>
        /// 添加助手消息
        /// </summary>
        void AddAssistantMessage(string content, Guid? serviceId = null, string? modelName = null);

        /// <summary>
        /// 取消对话
        /// </summary>
        Task CancelConversationAsync(string reason = "");

        /// <summary>
        /// 暂停对话
        /// </summary>
        Task PauseConversationAsync();

        /// <summary>
        /// 恢复对话
        /// </summary>
        Task ResumeConversationAsync();

        /// <summary>
        /// 获取对话进度
        /// </summary>
        ConversationProgress GetProgress();
    }

    /// <summary>
    /// 任务调度引擎接口
    /// </summary>
    public interface ITaskSchedulingEngine
    {
        Task<List<AITask>> ScheduleInitialTasksAsync(string userInput);
        Task<List<AITask>> ScheduleNextTasksAsync(AITask completedTask);
        Task<List<Guid>> DetermineTaskDependenciesAsync(AITask task);
        Task<int> CalculateTaskPriorityAsync(AITask task);
        Task<List<AITask>> OptimizeTaskOrderAsync(List<AITask> tasks);
        Task<bool> DetectCircularDependenciesAsync(List<AITask> tasks);
        Task<ExecutionPlan> CreateTaskExecutionPlanAsync(List<AITask> tasks);
        Task<TaskFailureStrategy> HandleTaskFailureAsync(AITask failedTask);
        Task<bool> ShouldContinueSchedulingAsync();
        Task<int> EstimateRemainingRoundsAsync();
        Task<AITask?> GetNextExecutableTaskAsync();
        Task UpdateTaskPrioritiesAsync(TaskCompletionResult result);
        Task<AnalysisResult> AnalyzeTaskResultsAsync(List<AITask> completedTasks);
        Task<List<AITask>> GenerateFollowUpTasksAsync(AnalysisResult analysis);
    }

    /// <summary>
    /// 任务执行器接口
    /// </summary>
    public interface ITaskExecutor
    {
        Task<TaskExecutionResult> ExecuteTaskAsync(AITask task);
        Task<List<TaskExecutionResult>> ExecuteTasksInParallelAsync(List<AITask> tasks);
        Task<List<TaskExecutionResult>> ExecuteTasksSequentiallyAsync(List<AITask> tasks);
        Task<TaskExecutionResult> ExecuteTaskWithRetryAsync(AITask task, RetryPolicy policy);
        Task<TaskExecutionResult> ExecuteTaskWithTimeoutAsync(AITask task, TimeSpan timeout);
        Task<TaskExecutionResult> ExecuteTaskWithCircuitBreakerAsync(AITask task);
        Task<AITask> PreprocessTaskAsync(AITask task);
        Task<TaskExecutionResult> PostprocessTaskAsync(AITask task, TaskExecutionResult result);
        Task<ValidationResult> ValidateTaskInputAsync(AITask task);
        Task<ValidationResult> ValidateTaskOutputAsync(TaskExecutionResult result);
        Task<AITask> TransformTaskInputAsync(AITask task);
        Task<TaskExecutionResult> TransformTaskOutputAsync(TaskExecutionResult result);
        Task<TaskExecutionResult> HandleTaskExceptionAsync(AITask task, Exception exception);
        IObservable<TaskExecutionProgress> MonitorTaskExecutionAsync(AITask task);
        Task<bool> CancelTaskExecutionAsync(Guid taskId);
        Task<TaskExecutionStatus> GetTaskExecutionStatusAsync(Guid taskId);
    }

    /// <summary>
    /// 对话流程控制器接口
    /// </summary>
    public interface IConversationFlowController
    {
        Task<ConversationResult> StartConversationAsync(string initialInput);
        Task<RoundResult> ProcessCurrentRoundAsync();
        Task<bool> AdvanceToNextRoundAsync(RoundResult currentResult);
        Task<NextAction> HandleRoundCompletionAsync(RoundResult result);
        Task<bool> DetermineConversationCompletionAsync();
        Task<ConversationResult> HandleConversationTimeoutAsync();
        Task<bool> PauseConversationAsync();
        Task<bool> ResumeConversationAsync();
        Task<bool> CancelConversationAsync(string reason);
        Task<bool> SaveConversationStateAsync();
        Task<bool> RestoreConversationStateAsync(Guid conversationId);
        Task<ConversationProgress> GetConversationProgressAsync();
        Task<TimeSpan?> EstimateRemainingTimeAsync();
        Task<List<IntermediateResult>> GetIntermediateResultsAsync();
    }

    /// <summary>
    /// 结果聚合器接口
    /// </summary>
    public interface IResultAggregator
    {
        Task<AggregatedResult> AggregateTaskResultsAsync(List<TaskExecutionResult> results);
        Task<ConversationResult> SynthesizeFinalAnswerAsync(List<AggregatedResult> roundResults);
        Task<ConversationResult> OptimizeResultQualityAsync(ConversationResult result);
        Task<ValidationResult> ValidateResultConsistencyAsync(ConversationResult result);
        Task<List<ConversationResult>> RankResultAlternativesAsync(List<ConversationResult> alternatives);
        Task<ConversationResult> MergePartialResultsAsync(List<PartialResult> partials);
        Task<List<TaskExecutionResult>> FilterRelevantResultsAsync(List<TaskExecutionResult> results);
        Task<Dictionary<Guid, double>> WeightResultImportanceAsync(List<TaskExecutionResult> results);
        Task<List<ResultConflict>> DetectResultConflictsAsync(List<TaskExecutionResult> results);
        Task<ConflictResolutionResult> ResolveResultConflictsAsync(List<ResultConflict> conflicts);
        Task<ResultSummary> GenerateResultSummaryAsync(ConversationResult result);
        Task<double> CalculateResultConfidenceAsync(ConversationResult result);
        Task<List<ResultGap>> IdentifyResultGapsAsync(ConversationResult result);
        Task<List<ImprovementSuggestion>> SuggestResultImprovementsAsync(ConversationResult result);
    }

    /// <summary>
    /// 自循环检测器接口
    /// </summary>
    public interface ISelfLoopDetector
    {
        Task<LoopDetectionResult> DetectLoopAsync(List<AITask> taskHistory);
        Task<LoopPattern> AnalyzeLoopPatternAsync(List<AITask> loopTasks);
        Task<int> CalculateLoopDepthAsync(List<AITask> taskHistory);
        Task<bool> IsInfiniteLoopAsync(LoopPattern pattern);
        Task<List<LoopBreakingStrategy>> GetLoopBreakingStrategiesAsync(LoopPattern pattern);
        Task<bool> ApplyLoopBreakingStrategyAsync(LoopBreakingStrategy strategy);
        Task RecordLoopOccurrenceAsync(LoopPattern pattern);
        Task<LoopStatistics> GetLoopStatisticsAsync();
        Task SetLoopDetectionThresholdAsync(int maxIterations);
        Task EnableLoopPreventionAsync(bool enable);
    }

    /// <summary>
    /// AI对话服务管理接口
    /// </summary>
    public interface IAITalkServiceManage
    {
        Task<List<AITalkServiceData>> GetAvailableServicesAsync();
        Task<AITalkServiceData?> SelectBestServiceAsync(TaskType taskType, Dictionary<string, object>? requirements = null);
        Task<bool> RegisterServiceAsync(AITalkServiceData serviceData);
        Task<bool> UnregisterServiceAsync(Guid serviceId);
        Task<HealthCheckResult> CheckServiceHealthAsync(Guid serviceId);
        Task<PerformanceReport> GetServicePerformanceAsync(Guid serviceId, TimeSpan period);
        Task<bool> UpdateServiceConfigurationAsync(Guid serviceId, ConfigurationSettings configuration);
        Task StartMonitoringAsync();
        Task StopMonitoringAsync();
        Task<ServiceStatistics> GetOverallStatisticsAsync();
        Task<List<ServiceAlert>> GetActiveAlertsAsync();
        Task<bool> HandleServiceFailureAsync(Guid serviceId, Exception error);
        Task<bool> TriggerServiceFailoverAsync(Guid serviceId);
    }
}
