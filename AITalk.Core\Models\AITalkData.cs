using System;
using System.Collections.Generic;
using System.Linq;
using AITalk.Core.Enums;
using AITalk.Core.Models.ConversationData;

namespace AITalk.Core.Models
{
    /// <summary>
    /// AI对话数据 - 存储对话相关的所有数据
    /// </summary>
    public class AITalkData
    {
        /// <summary>
        /// 数据唯一标识
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 对话基础信息
        /// </summary>
        public ConversationInfo ConversationInfo { get; set; } = new();

        /// <summary>
        /// 消息历史
        /// </summary>
        public MessageHistory MessageHistory { get; set; } = new();

        /// <summary>
        /// 任务队列状态
        /// </summary>
        public TaskQueueState TaskQueue { get; set; } = new();

        /// <summary>
        /// 上下文信息
        /// </summary>
        public ContextInformation Context { get; set; } = new();

        /// <summary>
        /// 对话统计信息
        /// </summary>
        public ConversationStatistics Statistics { get; set; } = new();

        /// <summary>
        /// 对话配置
        /// </summary>
        public ConversationConfiguration Configuration { get; set; } = new();

        /// <summary>
        /// 对话状态
        /// </summary>
        public ConversationState State { get; set; } = new();

        /// <summary>
        /// 对话结果
        /// </summary>
        public ConversationResult? Result { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 是否已删除（软删除）
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; } = new();

        /// <summary>
        /// 添加消息
        /// </summary>
        public void AddMessage(ConversationMessage message)
        {
            MessageHistory.AddMessage(message);
            ConversationInfo.LastActivityAt = DateTime.UtcNow;
            UpdateStatistics();
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 添加任务
        /// </summary>
        public bool AddTask(AITask task)
        {
            var success = TaskQueue.EnqueueTask(task);
            if (success)
            {
                ConversationInfo.LastActivityAt = DateTime.UtcNow;
                LastUpdated = DateTime.UtcNow;
            }
            return success;
        }

        /// <summary>
        /// 获取下一个待执行任务
        /// </summary>
        public AITask? GetNextTask()
        {
            return TaskQueue.DequeueTask();
        }

        /// <summary>
        /// 开始执行任务
        /// </summary>
        public bool StartTask(AITask task)
        {
            var success = TaskQueue.StartExecutingTask(task);
            if (success)
            {
                ConversationInfo.LastActivityAt = DateTime.UtcNow;
                LastUpdated = DateTime.UtcNow;
            }
            return success;
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        public void CompleteTask(AITask task, object? output = null)
        {
            task.Complete(output);
            TaskQueue.CompleteTask(task);
            
            // 更新上下文中的任务输出
            Context.TaskOutputs[task.TaskId] = output ?? new object();
            
            ConversationInfo.LastActivityAt = DateTime.UtcNow;
            UpdateStatistics();
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 任务失败
        /// </summary>
        public void FailTask(AITask task, Exception error)
        {
            task.Fail(error);
            TaskQueue.FailTask(task);
            
            ConversationInfo.LastActivityAt = DateTime.UtcNow;
            UpdateStatistics();
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        public void CancelTask(AITask task, string reason = "")
        {
            task.Cancel(reason);
            TaskQueue.CancelTask(task);
            
            ConversationInfo.LastActivityAt = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新对话状态
        /// </summary>
        public void UpdateStatus(ConversationStatus status, string reason = "")
        {
            ConversationInfo.UpdateStatus(status, reason);
            State.CurrentStatus = status;
            State.LastStatusChange = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            Statistics.TotalMessages = MessageHistory.TotalMessageCount;
            Statistics.UserMessages = MessageHistory.UserMessageCount;
            Statistics.AssistantMessages = MessageHistory.AssistantMessageCount;
            Statistics.SystemMessages = MessageHistory.SystemMessageCount;
            
            Statistics.TotalRounds = GetCurrentRound();
            Statistics.TotalTokens = MessageHistory.Messages.Sum(m => m.TokenCount);
            Statistics.TotalProcessingTime = TimeSpan.FromMilliseconds(
                MessageHistory.Messages.Sum(m => m.ProcessingTime.TotalMilliseconds));
            
            if (MessageHistory.Messages.Count > 0)
            {
                Statistics.AverageResponseTime = TimeSpan.FromMilliseconds(
                    MessageHistory.Messages.Average(m => m.ProcessingTime.TotalMilliseconds));
            }
            
            Statistics.ErrorCount = TaskQueue.FailedTasks.Count;
            Statistics.RetryCount = TaskQueue.CompletedTasks.Sum(t => t.RetryCount) + 
                                   TaskQueue.FailedTasks.Sum(t => t.RetryCount);
            
            Statistics.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 获取当前轮次
        /// </summary>
        public int GetCurrentRound()
        {
            var allTasks = new List<AITask>();
            allTasks.AddRange(TaskQueue.CompletedTasks);
            allTasks.AddRange(TaskQueue.FailedTasks);
            allTasks.AddRange(TaskQueue.CancelledTasks);
            allTasks.AddRange(TaskQueue.ExecutingTasks.Values);
            
            return allTasks.Count > 0 ? allTasks.Max(t => t.Round) : 1;
        }

        /// <summary>
        /// 检查对话是否完成
        /// </summary>
        public bool IsCompleted()
        {
            return ConversationInfo.Status == ConversationStatus.Completed ||
                   ConversationInfo.Status == ConversationStatus.Failed ||
                   ConversationInfo.Status == ConversationStatus.Cancelled;
        }

        /// <summary>
        /// 检查对话是否活跃
        /// </summary>
        public bool IsActive()
        {
            return ConversationInfo.IsActive() && 
                   (TaskQueue.PendingTasks.Count > 0 || TaskQueue.ExecutingTasks.Count > 0);
        }

        /// <summary>
        /// 获取对话摘要
        /// </summary>
        public ConversationSummary GetSummary()
        {
            return new ConversationSummary
            {
                ConversationId = ConversationInfo.ConversationId,
                Title = ConversationInfo.Title,
                Status = ConversationInfo.Status,
                Duration = ConversationInfo.GetDuration(),
                MessageCount = MessageHistory.TotalMessageCount,
                TaskCount = TaskQueue.CompletedTasks.Count + TaskQueue.FailedTasks.Count + TaskQueue.CancelledTasks.Count,
                TokensUsed = Statistics.TotalTokens,
                Cost = Statistics.TotalCost,
                CreatedAt = ConversationInfo.CreatedAt,
                LastActivity = ConversationInfo.LastActivityAt
            };
        }

        /// <summary>
        /// 清理过期数据
        /// </summary>
        public void CleanupExpiredData(TimeSpan maxAge)
        {
            // 清理旧消息
            MessageHistory.CleanupOldMessages(maxAge);
            
            // 清理旧任务（保留最近的一些任务用于分析）
            var cutoffTime = DateTime.UtcNow - maxAge;
            
            TaskQueue.CompletedTasks.RemoveAll(t => t.CompletedAt < cutoffTime);
            TaskQueue.FailedTasks.RemoveAll(t => t.CompletedAt < cutoffTime);
            TaskQueue.CancelledTasks.RemoveAll(t => t.CompletedAt < cutoffTime);
            
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 导出对话数据
        /// </summary>
        public string ExportToJson()
        {
            return System.Text.Json.JsonSerializer.Serialize(this, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            });
        }

        /// <summary>
        /// 验证数据完整性
        /// </summary>
        public List<string> ValidateIntegrity()
        {
            var issues = new List<string>();
            
            // 检查基本信息
            if (ConversationInfo.ConversationId == Guid.Empty)
                issues.Add("ConversationId is empty");
            
            if (string.IsNullOrEmpty(ConversationInfo.UserId))
                issues.Add("UserId is empty");
            
            // 检查消息历史
            if (MessageHistory.Messages.Any(m => m.MessageId == Guid.Empty))
                issues.Add("Some messages have empty MessageId");
            
            // 检查任务队列
            var allTasks = new List<AITask>();
            allTasks.AddRange(TaskQueue.CompletedTasks);
            allTasks.AddRange(TaskQueue.FailedTasks);
            allTasks.AddRange(TaskQueue.CancelledTasks);
            allTasks.AddRange(TaskQueue.ExecutingTasks.Values);
            
            if (allTasks.Any(t => t.TaskId == Guid.Empty))
                issues.Add("Some tasks have empty TaskId");
            
            // 检查任务依赖关系
            var taskIds = allTasks.Select(t => t.TaskId).ToHashSet();
            foreach (var task in allTasks)
            {
                foreach (var depId in task.Dependencies)
                {
                    if (!taskIds.Contains(depId))
                        issues.Add($"Task {task.TaskId} has missing dependency {depId}");
                }
            }
            
            return issues;
        }
    }
}
