using System;
using System.Collections.Generic;
using System.Linq;
using AITalk.Core.Enums;
using AITalk.Core.Models.ConversationData;

namespace AITalk.Core.Models
{
    /// <summary>
    /// AI对话数据 - 存储AITalk类的所有数据，支持自循环和多轮调度
    /// </summary>
    public class AITalkData
    {
        /// <summary>
        /// 对话唯一标识
        /// </summary>
        public Guid ConversationId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 对话名称
        /// </summary>
        public string ConversationName { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 对话类型
        /// </summary>
        public ConversationType ConversationType { get; set; } = ConversationType.MultiRound;

        /// <summary>
        /// 对话状态
        /// </summary>
        public ConversationStatus Status { get; set; } = ConversationStatus.Created;

        // === 多轮任务调度核心数据 ===
        /// <summary>
        /// 任务队列 - AITalk的核心，支持多轮调度
        /// </summary>
        public List<AITask> TaskQueue { get; set; } = new();

        /// <summary>
        /// 任务注册表 - 快速查找任务
        /// </summary>
        public Dictionary<Guid, AITask> TaskRegistry { get; set; } = new();

        /// <summary>
        /// 任务依赖关系图
        /// </summary>
        public Dictionary<Guid, List<Guid>> TaskDependencies { get; set; } = new();

        /// <summary>
        /// 执行队列 - 当前正在执行的任务
        /// </summary>
        public Queue<AITask> ExecutionQueue { get; set; } = new();

        /// <summary>
        /// 已完成的任务
        /// </summary>
        public List<AITask> CompletedTasks { get; set; } = new();

        /// <summary>
        /// 失败的任务
        /// </summary>
        public List<AITask> FailedTasks { get; set; } = new();

        /// <summary>
        /// 当前执行的任务
        /// </summary>
        public AITask? CurrentExecutingTask { get; set; }

        /// <summary>
        /// 最大并发任务数
        /// </summary>
        public int MaxConcurrentTasks { get; set; } = 3;

        /// <summary>
        /// 当前并发任务数
        /// </summary>
        public int CurrentConcurrentTasks { get; set; } = 0;

        // === 自循环检测和控制数据 ===
        /// <summary>
        /// 循环检测映射 - 用于检测重复的任务模式
        /// </summary>
        public Dictionary<string, int> LoopDetectionMap { get; set; } = new();

        /// <summary>
        /// 对话轮次历史
        /// </summary>
        public List<ConversationRound> ConversationRounds { get; set; } = new();

        /// <summary>
        /// 最大循环迭代次数
        /// </summary>
        public int MaxLoopIterations { get; set; } = 10;

        /// <summary>
        /// 当前循环计数
        /// </summary>
        public int CurrentLoopCount { get; set; } = 0;

        /// <summary>
        /// 是否处于循环状态
        /// </summary>
        public bool IsInLoop { get; set; } = false;

        /// <summary>
        /// 循环检测结果
        /// </summary>
        public LoopDetectionResult LoopDetectionResult { get; set; } = LoopDetectionResult.NoLoop;

        /// <summary>
        /// 循环中断策略
        /// </summary>
        public LoopBreakingStrategy LoopBreakingStrategy { get; set; } = LoopBreakingStrategy.LimitIterations;

        // === 对话内容数据 ===
        /// <summary>
        /// 消息历史
        /// </summary>
        public List<ConversationMessage> Messages { get; set; } = new();

        /// <summary>
        /// 对话上下文
        /// </summary>
        public Dictionary<string, object> ConversationContext { get; set; } = new();

        /// <summary>
        /// 任务输出缓存
        /// </summary>
        public Dictionary<Guid, object> TaskOutputs { get; set; } = new();

        /// <summary>
        /// 中间结果存储
        /// </summary>
        public Dictionary<string, object> IntermediateResults { get; set; } = new();

        /// <summary>
        /// 最终结果
        /// </summary>
        public object? FinalResult { get; set; }

        // === 配置和限制 ===
        /// <summary>
        /// 最大轮次
        /// </summary>
        public int MaxRounds { get; set; } = 50;

        /// <summary>
        /// 当前轮次
        /// </summary>
        public int CurrentRound { get; set; } = 1;

        /// <summary>
        /// 最大消息数
        /// </summary>
        public int MaxMessages { get; set; } = 1000;

        /// <summary>
        /// 超时时间
        /// </summary>
        public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(30);

        /// <summary>
        /// 是否启用自动保存
        /// </summary>
        public bool EnableAutoSave { get; set; } = true;

        /// <summary>
        /// 自动保存间隔
        /// </summary>
        public TimeSpan AutoSaveInterval { get; set; } = TimeSpan.FromMinutes(5);

        // === 时间戳和元数据 ===
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 是否已删除（软删除）
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; } = new();

        /// <summary>
        /// 添加消息
        /// </summary>
        public void AddMessage(ConversationMessage message)
        {
            MessageHistory.AddMessage(message);
            ConversationInfo.LastActivityAt = DateTime.UtcNow;
            UpdateStatistics();
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 添加任务
        /// </summary>
        public bool AddTask(AITask task)
        {
            var success = TaskQueue.EnqueueTask(task);
            if (success)
            {
                ConversationInfo.LastActivityAt = DateTime.UtcNow;
                LastUpdated = DateTime.UtcNow;
            }
            return success;
        }

        /// <summary>
        /// 获取下一个待执行任务
        /// </summary>
        public AITask? GetNextTask()
        {
            return TaskQueue.DequeueTask();
        }

        /// <summary>
        /// 开始执行任务
        /// </summary>
        public bool StartTask(AITask task)
        {
            var success = TaskQueue.StartExecutingTask(task);
            if (success)
            {
                ConversationInfo.LastActivityAt = DateTime.UtcNow;
                LastUpdated = DateTime.UtcNow;
            }
            return success;
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        public void CompleteTask(AITask task, object? output = null)
        {
            task.Complete(output);
            TaskQueue.CompleteTask(task);
            
            // 更新上下文中的任务输出
            Context.TaskOutputs[task.TaskId] = output ?? new object();
            
            ConversationInfo.LastActivityAt = DateTime.UtcNow;
            UpdateStatistics();
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 任务失败
        /// </summary>
        public void FailTask(AITask task, Exception error)
        {
            task.Fail(error);
            TaskQueue.FailTask(task);
            
            ConversationInfo.LastActivityAt = DateTime.UtcNow;
            UpdateStatistics();
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        public void CancelTask(AITask task, string reason = "")
        {
            task.Cancel(reason);
            TaskQueue.CancelTask(task);
            
            ConversationInfo.LastActivityAt = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新对话状态
        /// </summary>
        public void UpdateStatus(ConversationStatus status, string reason = "")
        {
            ConversationInfo.UpdateStatus(status, reason);
            State.CurrentStatus = status;
            State.LastStatusChange = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            Statistics.TotalMessages = MessageHistory.TotalMessageCount;
            Statistics.UserMessages = MessageHistory.UserMessageCount;
            Statistics.AssistantMessages = MessageHistory.AssistantMessageCount;
            Statistics.SystemMessages = MessageHistory.SystemMessageCount;
            
            Statistics.TotalRounds = GetCurrentRound();
            Statistics.TotalTokens = MessageHistory.Messages.Sum(m => m.TokenCount);
            Statistics.TotalProcessingTime = TimeSpan.FromMilliseconds(
                MessageHistory.Messages.Sum(m => m.ProcessingTime.TotalMilliseconds));
            
            if (MessageHistory.Messages.Count > 0)
            {
                Statistics.AverageResponseTime = TimeSpan.FromMilliseconds(
                    MessageHistory.Messages.Average(m => m.ProcessingTime.TotalMilliseconds));
            }
            
            Statistics.ErrorCount = TaskQueue.FailedTasks.Count;
            Statistics.RetryCount = TaskQueue.CompletedTasks.Sum(t => t.RetryCount) + 
                                   TaskQueue.FailedTasks.Sum(t => t.RetryCount);
            
            Statistics.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 获取当前轮次
        /// </summary>
        public int GetCurrentRound()
        {
            var allTasks = new List<AITask>();
            allTasks.AddRange(TaskQueue.CompletedTasks);
            allTasks.AddRange(TaskQueue.FailedTasks);
            allTasks.AddRange(TaskQueue.CancelledTasks);
            allTasks.AddRange(TaskQueue.ExecutingTasks.Values);
            
            return allTasks.Count > 0 ? allTasks.Max(t => t.Round) : 1;
        }

        /// <summary>
        /// 检查对话是否完成
        /// </summary>
        public bool IsCompleted()
        {
            return ConversationInfo.Status == ConversationStatus.Completed ||
                   ConversationInfo.Status == ConversationStatus.Failed ||
                   ConversationInfo.Status == ConversationStatus.Cancelled;
        }

        /// <summary>
        /// 检查对话是否活跃
        /// </summary>
        public bool IsActive()
        {
            return ConversationInfo.IsActive() && 
                   (TaskQueue.PendingTasks.Count > 0 || TaskQueue.ExecutingTasks.Count > 0);
        }

        /// <summary>
        /// 获取对话摘要
        /// </summary>
        public ConversationSummary GetSummary()
        {
            return new ConversationSummary
            {
                ConversationId = ConversationInfo.ConversationId,
                Title = ConversationInfo.Title,
                Status = ConversationInfo.Status,
                Duration = ConversationInfo.GetDuration(),
                MessageCount = MessageHistory.TotalMessageCount,
                TaskCount = TaskQueue.CompletedTasks.Count + TaskQueue.FailedTasks.Count + TaskQueue.CancelledTasks.Count,
                TokensUsed = Statistics.TotalTokens,
                Cost = Statistics.TotalCost,
                CreatedAt = ConversationInfo.CreatedAt,
                LastActivity = ConversationInfo.LastActivityAt
            };
        }

        /// <summary>
        /// 清理过期数据
        /// </summary>
        public void CleanupExpiredData(TimeSpan maxAge)
        {
            // 清理旧消息
            MessageHistory.CleanupOldMessages(maxAge);
            
            // 清理旧任务（保留最近的一些任务用于分析）
            var cutoffTime = DateTime.UtcNow - maxAge;
            
            TaskQueue.CompletedTasks.RemoveAll(t => t.CompletedAt < cutoffTime);
            TaskQueue.FailedTasks.RemoveAll(t => t.CompletedAt < cutoffTime);
            TaskQueue.CancelledTasks.RemoveAll(t => t.CompletedAt < cutoffTime);
            
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 导出对话数据
        /// </summary>
        public string ExportToJson()
        {
            return System.Text.Json.JsonSerializer.Serialize(this, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            });
        }

        /// <summary>
        /// 验证数据完整性
        /// </summary>
        public List<string> ValidateIntegrity()
        {
            var issues = new List<string>();
            
            // 检查基本信息
            if (ConversationInfo.ConversationId == Guid.Empty)
                issues.Add("ConversationId is empty");
            
            if (string.IsNullOrEmpty(ConversationInfo.UserId))
                issues.Add("UserId is empty");
            
            // 检查消息历史
            if (MessageHistory.Messages.Any(m => m.MessageId == Guid.Empty))
                issues.Add("Some messages have empty MessageId");
            
            // 检查任务队列
            var allTasks = new List<AITask>();
            allTasks.AddRange(TaskQueue.CompletedTasks);
            allTasks.AddRange(TaskQueue.FailedTasks);
            allTasks.AddRange(TaskQueue.CancelledTasks);
            allTasks.AddRange(TaskQueue.ExecutingTasks.Values);
            
            if (allTasks.Any(t => t.TaskId == Guid.Empty))
                issues.Add("Some tasks have empty TaskId");
            
            // 检查任务依赖关系
            var taskIds = allTasks.Select(t => t.TaskId).ToHashSet();
            foreach (var task in allTasks)
            {
                foreach (var depId in task.Dependencies)
                {
                    if (!taskIds.Contains(depId))
                        issues.Add($"Task {task.TaskId} has missing dependency {depId}");
                }
            }
            
            return issues;
        }
    }
}
