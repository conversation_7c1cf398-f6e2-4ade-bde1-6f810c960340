using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AITalk.Core.Enums;
using AITalk.Core.Models;
using AITalk.Core.Models.ServiceData;
using AITalk.Core.Services.Interfaces;

namespace AITalk.Core.Services
{
    /// <summary>
    /// AI对话服务 - 主要管理第三方服务器的地址、方法、API Key等
    /// </summary>
    public class AITalkService : IAITalkService
    {
        private readonly IServiceDiscovery _serviceDiscovery;
        private readonly ILoadBalancer _loadBalancer;
        private readonly IApiClient _apiClient;
        private readonly IPerformanceMonitor _performanceMonitor;
        private readonly IConfigurationManager _configurationManager;
        private readonly ISecurityManager _securityManager;

        public AITalkService(
            IServiceDiscovery serviceDiscovery,
            ILoadBalancer loadBalancer,
            IApiClient apiClient,
            IPerformanceMonitor performanceMonitor,
            IConfigurationManager configurationManager,
            ISecurityManager securityManager)
        {
            _serviceDiscovery = serviceDiscovery ?? throw new ArgumentNullException(nameof(serviceDiscovery));
            _loadBalancer = loadBalancer ?? throw new ArgumentNullException(nameof(loadBalancer));
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _securityManager = securityManager ?? throw new ArgumentNullException(nameof(securityManager));
        }

        #region 服务发现和注册

        /// <summary>
        /// 注册服务
        /// </summary>
        public async Task<bool> RegisterServiceAsync(AITalkServiceData serviceData)
        {
            try
            {
                // 验证服务配置
                var validationResult = await ValidateServiceConfigurationAsync(serviceData);
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"Service configuration is invalid: {string.Join(", ", validationResult.Errors)}");
                }

                // 注册服务
                var success = await _serviceDiscovery.RegisterServiceAsync(serviceData);
                if (success)
                {
                    // 开始监控
                    await _performanceMonitor.StartMonitoringAsync(serviceData.Id);
                    
                    // 记录注册事件
                    serviceData.Health.StatusMessage = "Service registered successfully";
                    serviceData.UpdateHealth(true, "Service registered and monitoring started");
                }

                return success;
            }
            catch (Exception ex)
            {
                serviceData.Health.ErrorLogs.Add(new ErrorLogEntry
                {
                    ErrorMessage = ex.Message,
                    StackTrace = ex.StackTrace ?? string.Empty,
                    Source = nameof(RegisterServiceAsync)
                });
                throw;
            }
        }

        /// <summary>
        /// 注销服务
        /// </summary>
        public async Task<bool> UnregisterServiceAsync(Guid serviceId)
        {
            try
            {
                // 停止监控
                await _performanceMonitor.StopMonitoringAsync(serviceId);
                
                // 注销服务
                return await _serviceDiscovery.UnregisterServiceAsync(serviceId);
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，因为注销操作应该尽力完成
                // 这里可以记录到日志系统
                return false;
            }
        }

        /// <summary>
        /// 发现服务
        /// </summary>
        public async Task<List<AITalkServiceData>> DiscoverServicesAsync()
        {
            return await _serviceDiscovery.DiscoverServicesAsync();
        }

        /// <summary>
        /// 根据ID获取服务
        /// </summary>
        public async Task<AITalkServiceData?> GetServiceByIdAsync(Guid serviceId)
        {
            return await _serviceDiscovery.GetServiceByIdAsync(serviceId);
        }

        /// <summary>
        /// 根据类型获取服务
        /// </summary>
        public async Task<List<AITalkServiceData>> GetServicesByTypeAsync(ServiceType serviceType)
        {
            return await _serviceDiscovery.GetServicesByTypeAsync(serviceType);
        }

        /// <summary>
        /// 获取健康的服务
        /// </summary>
        public async Task<List<AITalkServiceData>> GetHealthyServicesAsync()
        {
            return await _serviceDiscovery.GetHealthyServicesAsync();
        }

        #endregion

        #region 负载均衡和服务选择

        /// <summary>
        /// 选择最佳服务
        /// </summary>
        public async Task<AITalkServiceData?> SelectBestServiceAsync(
            List<AITalkServiceData> availableServices, 
            LoadBalancingStrategy strategy = LoadBalancingStrategy.HealthBased)
        {
            if (availableServices == null || availableServices.Count == 0)
                return null;

            // 过滤可用服务
            var healthyServices = availableServices.FindAll(s => s.IsAvailable());
            if (healthyServices.Count == 0)
                return null;

            return _loadBalancer.SelectService(healthyServices, strategy);
        }

        /// <summary>
        /// 选择服务用于特定任务
        /// </summary>
        public async Task<AITalkServiceData?> SelectServiceForTaskAsync(
            TaskType taskType, 
            Dictionary<string, object>? requirements = null)
        {
            // 获取所有健康服务
            var healthyServices = await GetHealthyServicesAsync();
            if (healthyServices.Count == 0)
                return null;

            // 根据任务类型过滤服务
            var suitableServices = FilterServicesByTaskType(healthyServices, taskType);
            if (suitableServices.Count == 0)
                return null;

            // 根据需求进一步过滤
            if (requirements != null)
            {
                suitableServices = FilterServicesByRequirements(suitableServices, requirements);
            }

            // 选择最佳服务
            return await SelectBestServiceAsync(suitableServices, LoadBalancingStrategy.HealthBased);
        }

        #endregion

        #region API调用

        /// <summary>
        /// 发送聊天完成请求
        /// </summary>
        public async Task<ApiResponse<ChatCompletionResponse>> SendChatCompletionAsync(
            AITalkServiceData service, 
            ChatCompletionRequest request)
        {
            try
            {
                // 验证认证
                var authResult = await _securityManager.AuthenticateServiceAsync(service.Id, service.Authentication);
                if (!authResult.IsSuccess)
                {
                    throw new UnauthorizedAccessException($"Authentication failed: {authResult.ErrorMessage}");
                }

                // 发送请求
                var response = await _apiClient.SendChatCompletionAsync(service, request);
                
                // 更新性能指标
                service.UpdatePerformanceMetrics(response.ResponseTime, response.IsSuccess);
                
                // 更新使用统计
                if (response.IsSuccess && response.Data != null)
                {
                    var tokensUsed = response.Data.Usage?.TotalTokens ?? 0;
                    var cost = CalculateCost(service, tokensUsed);
                    service.UpdateUsageStatistics(tokensUsed, cost);
                }

                return response;
            }
            catch (Exception ex)
            {
                // 记录错误
                service.Health.ErrorLogs.Add(new ErrorLogEntry
                {
                    ErrorMessage = ex.Message,
                    StackTrace = ex.StackTrace ?? string.Empty,
                    Source = nameof(SendChatCompletionAsync)
                });
                
                // 更新健康状态
                service.UpdateHealth(false, $"API call failed: {ex.Message}");
                
                throw;
            }
        }

        /// <summary>
        /// 发送流式请求
        /// </summary>
        public async IAsyncEnumerable<StreamingResponse> SendStreamingRequestAsync(
            AITalkServiceData service, 
            StreamingRequest request)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                // 验证认证
                var authResult = await _securityManager.AuthenticateServiceAsync(service.Id, service.Authentication);
                if (!authResult.IsSuccess)
                {
                    throw new UnauthorizedAccessException($"Authentication failed: {authResult.ErrorMessage}");
                }

                await foreach (var chunk in _apiClient.SendStreamingRequestAsync(service, request))
                {
                    yield return chunk;
                }
                
                // 更新成功指标
                var duration = DateTime.UtcNow - startTime;
                service.UpdatePerformanceMetrics(duration, true);
            }
            catch (Exception ex)
            {
                // 记录错误
                service.Health.ErrorLogs.Add(new ErrorLogEntry
                {
                    ErrorMessage = ex.Message,
                    StackTrace = ex.StackTrace ?? string.Empty,
                    Source = nameof(SendStreamingRequestAsync)
                });
                
                // 更新健康状态
                var duration = DateTime.UtcNow - startTime;
                service.UpdatePerformanceMetrics(duration, false);
                service.UpdateHealth(false, $"Streaming request failed: {ex.Message}");
                
                throw;
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新服务配置
        /// </summary>
        public async Task<bool> UpdateServiceConfigurationAsync(Guid serviceId, ConfigurationSettings configuration)
        {
            try
            {
                var service = await GetServiceByIdAsync(serviceId);
                if (service == null)
                    return false;

                // 验证配置
                var validationResult = ValidateConfiguration(configuration);
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"Configuration is invalid: {string.Join(", ", validationResult.Errors)}");
                }

                // 更新配置
                service.Configuration = configuration;
                service.LastUpdated = DateTime.UtcNow;

                // 应用配置更改
                return await _configurationManager.ApplyConfigurationChangesAsync(serviceId, configuration);
            }
            catch (Exception ex)
            {
                // 记录错误
                throw new InvalidOperationException($"Failed to update service configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取服务配置
        /// </summary>
        public async Task<ConfigurationSettings?> GetServiceConfigurationAsync(Guid serviceId)
        {
            var service = await GetServiceByIdAsync(serviceId);
            return service?.Configuration;
        }

        #endregion

        #region 健康检查和监控

        /// <summary>
        /// 执行健康检查
        /// </summary>
        public async Task<HealthCheckResult> PerformHealthCheckAsync(Guid serviceId)
        {
            try
            {
                var service = await GetServiceByIdAsync(serviceId);
                if (service == null)
                {
                    return new HealthCheckResult
                    {
                        IsHealthy = false,
                        Message = "Service not found",
                        CheckedAt = DateTime.UtcNow
                    };
                }

                // 执行基本连接测试
                var isReachable = await TestServiceConnectivity(service);
                
                // 更新健康状态
                service.UpdateHealth(isReachable, isReachable ? "Health check passed" : "Health check failed");
                
                return new HealthCheckResult
                {
                    IsHealthy = isReachable,
                    Message = service.Health.StatusMessage,
                    CheckedAt = DateTime.UtcNow,
                    ResponseTime = service.Performance.AverageResponseTime,
                    Details = new Dictionary<string, object>
                    {
                        { "HealthScore", service.Health.HealthScore },
                        { "ConsecutiveFailures", service.Health.ConsecutiveFailures },
                        { "LastSuccessfulRequest", service.Health.LastSuccessfulRequest },
                        { "ErrorRate", service.Performance.ErrorRate }
                    }
                };
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    IsHealthy = false,
                    Message = $"Health check failed: {ex.Message}",
                    CheckedAt = DateTime.UtcNow
                };
            }
        }

        /// <summary>
        /// 获取服务性能报告
        /// </summary>
        public async Task<PerformanceReport> GetPerformanceReportAsync(Guid serviceId, TimeSpan period)
        {
            return await _performanceMonitor.GeneratePerformanceReportAsync(serviceId, period);
        }

        #endregion

        #region 私有辅助方法

        private async Task<ValidationResult> ValidateServiceConfigurationAsync(AITalkServiceData serviceData)
        {
            var result = new ValidationResult { IsValid = true };
            
            // 验证基本信息
            if (string.IsNullOrEmpty(serviceData.ServerInfo.BaseUrl))
            {
                result.IsValid = false;
                result.Errors.Add("BaseUrl is required");
            }
            
            if (string.IsNullOrEmpty(serviceData.Authentication.ApiKey) && 
                serviceData.Authentication.AuthType == AuthenticationType.ApiKey)
            {
                result.IsValid = false;
                result.Errors.Add("ApiKey is required for ApiKey authentication");
            }

            // 验证URL格式
            if (!string.IsNullOrEmpty(serviceData.ServerInfo.BaseUrl) && 
                !Uri.TryCreate(serviceData.ServerInfo.BaseUrl, UriKind.Absolute, out _))
            {
                result.IsValid = false;
                result.Errors.Add("BaseUrl is not a valid URL");
            }

            return result;
        }

        private ValidationResult ValidateConfiguration(ConfigurationSettings configuration)
        {
            var result = new ValidationResult { IsValid = true };
            
            if (configuration.ConnectionTimeout <= TimeSpan.Zero)
            {
                result.IsValid = false;
                result.Errors.Add("ConnectionTimeout must be positive");
            }
            
            if (configuration.MaxRetryAttempts < 0)
            {
                result.IsValid = false;
                result.Errors.Add("MaxRetryAttempts cannot be negative");
            }

            return result;
        }

        private List<AITalkServiceData> FilterServicesByTaskType(List<AITalkServiceData> services, TaskType taskType)
        {
            // 根据任务类型过滤服务
            // 这里可以根据服务能力和任务类型进行匹配
            return services.FindAll(s => 
                s.Capabilities.SupportedFeatures.Contains(taskType.ToString()) ||
                s.Capabilities.SupportedModelTypes.Count > 0);
        }

        private List<AITalkServiceData> FilterServicesByRequirements(
            List<AITalkServiceData> services, 
            Dictionary<string, object> requirements)
        {
            var filtered = new List<AITalkServiceData>();
            
            foreach (var service in services)
            {
                bool meetsRequirements = true;
                
                foreach (var requirement in requirements)
                {
                    switch (requirement.Key.ToLower())
                    {
                        case "maxcontextlength":
                            if (requirement.Value is int maxContext && 
                                service.Capabilities.MaxContextLength < maxContext)
                                meetsRequirements = false;
                            break;
                        case "supportsfunctioncalling":
                            if (requirement.Value is bool needsFunctions && 
                                needsFunctions && !service.Capabilities.SupportsFunctionCalling)
                                meetsRequirements = false;
                            break;
                        case "supportsstreaming":
                            if (requirement.Value is bool needsStreaming && 
                                needsStreaming && !service.Capabilities.SupportsStreaming)
                                meetsRequirements = false;
                            break;
                    }
                    
                    if (!meetsRequirements) break;
                }
                
                if (meetsRequirements)
                    filtered.Add(service);
            }
            
            return filtered;
        }

        private async Task<bool> TestServiceConnectivity(AITalkServiceData service)
        {
            try
            {
                // 这里可以实现简单的连接测试
                // 例如发送一个健康检查请求
                var testRequest = new ApiRequest
                {
                    Endpoint = "/health",
                    Method = "GET",
                    Timeout = TimeSpan.FromSeconds(10)
                };
                
                var response = await _apiClient.SendRequestAsync<object>(service, testRequest);
                return response.IsSuccess;
            }
            catch
            {
                return false;
            }
        }

        private decimal CalculateCost(AITalkServiceData service, long tokensUsed)
        {
            if (service.Pricing.InputTokenPrice > 0)
            {
                return (decimal)tokensUsed * service.Pricing.InputTokenPrice;
            }
            return 0;
        }

        #endregion
    }
}
