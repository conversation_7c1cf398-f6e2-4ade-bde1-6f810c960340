using System;

namespace AITalk.Core.Enums
{
    /// <summary>
    /// 服务器类型枚举
    /// </summary>
    public enum ServiceType
    {
        OpenAI,
        <PERSON>,
        Gemini,
        LocalModel,
        Azure,
        Cohere,
        HuggingFace,
        Custom
    }

    /// <summary>
    /// 认证类型枚举
    /// </summary>
    public enum AuthenticationType
    {
        ApiKey,
        OAuth2,
        JWT,
        Basic,
        BearerToken,
        Custom
    }

    /// <summary>
    /// 协议类型枚举
    /// </summary>
    public enum ProtocolType
    {
        HTTP,
        HTTPS,
        WebSocket,
        gRPC
    }

    /// <summary>
    /// 服务状态枚举
    /// </summary>
    public enum ServiceStatus
    {
        Unknown,
        Online,
        Offline,
        Maintenance,
        Error,
        Degraded
    }

    /// <summary>
    /// 负载均衡策略枚举
    /// </summary>
    public enum LoadBalancingStrategy
    {
        RoundRobin,
        WeightedRoundRobin,
        LeastConnections,
        ResponseTimeBased,
        HealthBased,
        GeographicProximity,
        CostOptimized,
        Random
    }

    /// <summary>
    /// 压缩级别枚举
    /// </summary>
    public enum CompressionLevel
    {
        None,
        Low,
        Medium,
        High,
        Maximum
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Trace,
        Debug,
        Information,
        Warning,
        Error,
        Critical,
        None
    }

    /// <summary>
    /// 熔断器状态枚举
    /// </summary>
    public enum CircuitBreakerState
    {
        Closed,
        Open,
        HalfOpen
    }

    /// <summary>
    /// 重试策略枚举
    /// </summary>
    public enum RetryStrategy
    {
        None,
        Fixed,
        Linear,
        Exponential,
        Custom
    }

    /// <summary>
    /// 队列溢出策略枚举
    /// </summary>
    public enum QueueOverflowStrategy
    {
        Drop,
        Block,
        Expand,
        Reject
    }

    /// <summary>
    /// 任务调度策略枚举
    /// </summary>
    public enum TaskSchedulingStrategy
    {
        Immediate,
        Batched,
        Scheduled,
        Priority,
        Custom
    }

    /// <summary>
    /// 任务执行顺序枚举
    /// </summary>
    public enum TaskExecutionOrder
    {
        FIFO,
        LIFO,
        Priority,
        Custom
    }
}
