using System;
using System.Collections.Generic;
using AITalk.Core.Enums;
using AITalk.Core.Models.ServiceData;

namespace AITalk.Core.Models
{
    /// <summary>
    /// AI对话服务数据 - 存储服务器相关的详细信息
    /// </summary>
    public class AITalkServiceData
    {
        /// <summary>
        /// 服务数据唯一标识
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 服务器基础信息
        /// </summary>
        public ServerInfo ServerInfo { get; set; } = new();

        /// <summary>
        /// 认证信息
        /// </summary>
        public AuthenticationInfo Authentication { get; set; } = new();

        /// <summary>
        /// 性能指标
        /// </summary>
        public PerformanceMetrics Performance { get; set; } = new();

        /// <summary>
        /// 使用统计
        /// </summary>
        public UsageStatistics Usage { get; set; } = new();

        /// <summary>
        /// 配置设置
        /// </summary>
        public ConfigurationSettings Configuration { get; set; } = new();

        /// <summary>
        /// 健康状态
        /// </summary>
        public HealthStatus Health { get; set; } = new();

        /// <summary>
        /// 服务能力信息
        /// </summary>
        public ServiceCapabilities Capabilities { get; set; } = new();

        /// <summary>
        /// 服务限制信息
        /// </summary>
        public ServiceLimits Limits { get; set; } = new();

        /// <summary>
        /// 定价信息
        /// </summary>
        public PricingInfo Pricing { get; set; } = new();

        /// <summary>
        /// 服务等级协议
        /// </summary>
        public ServiceLevelAgreement SLA { get; set; } = new();

        /// <summary>
        /// 监控配置
        /// </summary>
        public MonitoringConfiguration Monitoring { get; set; } = new();

        /// <summary>
        /// 备份和恢复配置
        /// </summary>
        public BackupConfiguration Backup { get; set; } = new();

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 最后更新者
        /// </summary>
        public string LastUpdatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 是否已删除（软删除）
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 删除者
        /// </summary>
        public string DeletedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 更新健康状态
        /// </summary>
        public void UpdateHealth(bool isHealthy, string statusMessage = "")
        {
            Health.IsHealthy = isHealthy;
            Health.LastHealthCheck = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(statusMessage))
            {
                Health.StatusMessage = statusMessage;
            }
            
            if (isHealthy)
            {
                Health.LastSuccessfulRequest = DateTime.UtcNow;
                Health.ConsecutiveFailures = 0;
                Health.HealthScore = Math.Min(100.0, Health.HealthScore + 5.0);
            }
            else
            {
                Health.LastFailedRequest = DateTime.UtcNow;
                Health.ConsecutiveFailures++;
                Health.HealthScore = Math.Max(0.0, Health.HealthScore - 10.0);
            }

            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新性能指标
        /// </summary>
        public void UpdatePerformanceMetrics(TimeSpan responseTime, bool isSuccess)
        {
            Performance.LastMeasuredAt = DateTime.UtcNow;
            
            if (isSuccess)
            {
                Usage.TotalSuccessfulRequests++;
                Performance.SuccessRate = (double)Usage.TotalSuccessfulRequests / Usage.TotalRequests;
            }
            else
            {
                Usage.TotalFailedRequests++;
                Performance.ErrorRate = (double)Usage.TotalFailedRequests / Usage.TotalRequests;
            }

            // 更新响应时间统计
            if (Performance.MinResponseTime == TimeSpan.Zero || responseTime < Performance.MinResponseTime)
                Performance.MinResponseTime = responseTime;
            
            if (responseTime > Performance.MaxResponseTime)
                Performance.MaxResponseTime = responseTime;

            // 简单的移动平均
            Performance.AverageResponseTime = TimeSpan.FromMilliseconds(
                (Performance.AverageResponseTime.TotalMilliseconds * 0.9) + 
                (responseTime.TotalMilliseconds * 0.1)
            );

            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新使用统计
        /// </summary>
        public void UpdateUsageStatistics(long tokensUsed, decimal cost)
        {
            Usage.TotalRequests++;
            Usage.TotalTokensUsed += tokensUsed;
            Usage.TotalCost += cost;
            
            var today = DateTime.UtcNow.Date;
            if (!Usage.DailyUsage.ContainsKey(today))
            {
                Usage.DailyUsage[today] = new UsageDayData { Date = today };
            }
            
            Usage.DailyUsage[today].Requests++;
            Usage.DailyUsage[today].TokensUsed += tokensUsed;
            Usage.DailyUsage[today].Cost += cost;

            Usage.LastUpdated = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 检查是否可用
        /// </summary>
        public bool IsAvailable()
        {
            return ServerInfo.IsActive && 
                   Health.IsHealthy && 
                   !Health.MaintenanceMode && 
                   !IsDeleted;
        }

        /// <summary>
        /// 获取服务权重（用于负载均衡）
        /// </summary>
        public double GetEffectiveWeight()
        {
            if (!IsAvailable()) return 0.0;
            
            // 基于健康评分和配置权重计算有效权重
            return ServerInfo.Weight * (Health.HealthScore / 100.0);
        }

        /// <summary>
        /// 克隆服务数据（深拷贝）
        /// </summary>
        public AITalkServiceData Clone()
        {
            // 这里应该实现深拷贝逻辑
            // 为了简化，这里只是创建新实例
            return new AITalkServiceData
            {
                Id = Guid.NewGuid(),
                ServerInfo = new ServerInfo
                {
                    ServerName = ServerInfo.ServerName + "_Clone",
                    ServerType = ServerInfo.ServerType,
                    BaseUrl = ServerInfo.BaseUrl,
                    // ... 其他属性
                },
                // ... 其他属性的拷贝
                CreatedAt = DateTime.UtcNow,
                LastUpdated = DateTime.UtcNow
            };
        }
    }
}
