using System;
using System.Collections.Generic;
using System.Net;
using AITalk.Core.Enums;
using AITalk.Core.Models.ServiceData;

namespace AITalk.Core.Models
{
    /// <summary>
    /// AI对话服务数据 - 存储服务器的所有详细信息（IP地址、key、响应速度、token数量等）
    /// </summary>
    public class AITalkServiceData
    {
        /// <summary>
        /// 服务数据唯一标识
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 服务名称
        /// </summary>
        public string ServiceName { get; set; } = string.Empty;

        /// <summary>
        /// 服务描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 服务提供商类型
        /// </summary>
        public ProviderType ProviderType { get; set; } = ProviderType.OpenAI;

        // === 服务器网络信息 ===
        /// <summary>
        /// 服务器主IP地址
        /// </summary>
        public string PrimaryIpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 服务器备用IP地址列表
        /// </summary>
        public List<string> BackupIpAddresses { get; set; } = new();

        /// <summary>
        /// 服务器端口
        /// </summary>
        public int ServerPort { get; set; } = 443;

        /// <summary>
        /// 基础URL
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// 备用URL列表
        /// </summary>
        public List<string> BackupUrls { get; set; } = new();

        /// <summary>
        /// API版本
        /// </summary>
        public string ApiVersion { get; set; } = "v1";

        /// <summary>
        /// 服务器地理位置
        /// </summary>
        public string ServerRegion { get; set; } = string.Empty;

        /// <summary>
        /// 数据中心位置
        /// </summary>
        public string DataCenter { get; set; } = string.Empty;

        /// <summary>
        /// CDN节点列表
        /// </summary>
        public List<string> CdnNodes { get; set; } = new();

        // === API密钥和认证信息 ===
        /// <summary>
        /// 主API密钥
        /// </summary>
        public string PrimaryApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 备用API密钥列表
        /// </summary>
        public List<string> BackupApiKeys { get; set; } = new();

        /// <summary>
        /// 当前使用的API密钥索引
        /// </summary>
        public int CurrentApiKeyIndex { get; set; } = 0;

        /// <summary>
        /// 组织ID
        /// </summary>
        public string OrganizationId { get; set; } = string.Empty;

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; } = string.Empty;

        /// <summary>
        /// 认证类型
        /// </summary>
        public AuthenticationType AuthType { get; set; } = AuthenticationType.ApiKey;

        /// <summary>
        /// OAuth令牌信息
        /// </summary>
        public string OAuthToken { get; set; } = string.Empty;

        /// <summary>
        /// OAuth刷新令牌
        /// </summary>
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// 令牌过期时间
        /// </summary>
        public DateTime? TokenExpiresAt { get; set; }

        /// <summary>
        /// 客户端证书路径
        /// </summary>
        public string ClientCertificatePath { get; set; } = string.Empty;

        /// <summary>
        /// 客户端证书密码
        /// </summary>
        public string ClientCertificatePassword { get; set; } = string.Empty;

        /// <summary>
        /// 是否使用SSL
        /// </summary>
        public bool UseSSL { get; set; } = true;

        /// <summary>
        /// SSL证书验证
        /// </summary>
        public bool ValidateSSLCertificate { get; set; } = true;

        // === 性能和响应速度信息 ===
        /// <summary>
        /// 平均响应时间（毫秒）
        /// </summary>
        public double AverageResponseTimeMs { get; set; } = 0.0;

        /// <summary>
        /// 最小响应时间（毫秒）
        /// </summary>
        public double MinResponseTimeMs { get; set; } = 0.0;

        /// <summary>
        /// 最大响应时间（毫秒）
        /// </summary>
        public double MaxResponseTimeMs { get; set; } = 0.0;

        /// <summary>
        /// 最近24小时响应时间历史
        /// </summary>
        public List<ResponseTimeRecord> ResponseTimeHistory { get; set; } = new();

        /// <summary>
        /// 网络延迟（毫秒）
        /// </summary>
        public double NetworkLatencyMs { get; set; } = 0.0;

        /// <summary>
        /// 吞吐量（每秒请求数）
        /// </summary>
        public double ThroughputPerSecond { get; set; } = 0.0;

        /// <summary>
        /// 并发连接数
        /// </summary>
        public int ConcurrentConnections { get; set; } = 0;

        /// <summary>
        /// 最大并发连接数
        /// </summary>
        public int MaxConcurrentConnections { get; set; } = 100;

        // === Token使用统计 ===
        /// <summary>
        /// 总Token使用量
        /// </summary>
        public long TotalTokensUsed { get; set; } = 0;

        /// <summary>
        /// 输入Token使用量
        /// </summary>
        public long InputTokensUsed { get; set; } = 0;

        /// <summary>
        /// 输出Token使用量
        /// </summary>
        public long OutputTokensUsed { get; set; } = 0;

        /// <summary>
        /// 今日Token使用量
        /// </summary>
        public long TodayTokensUsed { get; set; } = 0;

        /// <summary>
        /// 本月Token使用量
        /// </summary>
        public long MonthlyTokensUsed { get; set; } = 0;

        /// <summary>
        /// Token使用历史（按日期）
        /// </summary>
        public Dictionary<DateTime, long> DailyTokenUsage { get; set; } = new();

        /// <summary>
        /// Token限制（每分钟）
        /// </summary>
        public long TokenLimitPerMinute { get; set; } = 0;

        /// <summary>
        /// Token限制（每天）
        /// </summary>
        public long TokenLimitPerDay { get; set; } = 0;

        // === 请求统计信息 ===
        /// <summary>
        /// 总请求数
        /// </summary>
        public long TotalRequests { get; set; } = 0;

        /// <summary>
        /// 成功请求数
        /// </summary>
        public long SuccessfulRequests { get; set; } = 0;

        /// <summary>
        /// 失败请求数
        /// </summary>
        public long FailedRequests { get; set; } = 0;

        /// <summary>
        /// 今日请求数
        /// </summary>
        public long TodayRequests { get; set; } = 0;

        /// <summary>
        /// 本月请求数
        /// </summary>
        public long MonthlyRequests { get; set; } = 0;

        /// <summary>
        /// 请求成功率
        /// </summary>
        public double SuccessRate { get; set; } = 100.0;

        /// <summary>
        /// 错误率
        /// </summary>
        public double ErrorRate { get; set; } = 0.0;

        /// <summary>
        /// 每日请求统计
        /// </summary>
        public Dictionary<DateTime, RequestDayData> DailyRequestStats { get; set; } = new();

        // === 健康状态和可用性 ===
        /// <summary>
        /// 服务是否健康
        /// </summary>
        public bool IsHealthy { get; set; } = true;

        /// <summary>
        /// 健康评分（0-100）
        /// </summary>
        public double HealthScore { get; set; } = 100.0;

        /// <summary>
        /// 服务可用性百分比
        /// </summary>
        public double AvailabilityPercentage { get; set; } = 100.0;

        /// <summary>
        /// 最后健康检查时间
        /// </summary>
        public DateTime LastHealthCheck { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后成功请求时间
        /// </summary>
        public DateTime LastSuccessfulRequest { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后失败请求时间
        /// </summary>
        public DateTime? LastFailedRequest { get; set; }

        /// <summary>
        /// 连续失败次数
        /// </summary>
        public int ConsecutiveFailures { get; set; } = 0;

        /// <summary>
        /// 是否处于维护模式
        /// </summary>
        public bool IsInMaintenanceMode { get; set; } = false;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 最后更新者
        /// </summary>
        public string LastUpdatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 是否已删除（软删除）
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 删除者
        /// </summary>
        public string DeletedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 更新健康状态
        /// </summary>
        public void UpdateHealth(bool isHealthy, string statusMessage = "")
        {
            Health.IsHealthy = isHealthy;
            Health.LastHealthCheck = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(statusMessage))
            {
                Health.StatusMessage = statusMessage;
            }
            
            if (isHealthy)
            {
                Health.LastSuccessfulRequest = DateTime.UtcNow;
                Health.ConsecutiveFailures = 0;
                Health.HealthScore = Math.Min(100.0, Health.HealthScore + 5.0);
            }
            else
            {
                Health.LastFailedRequest = DateTime.UtcNow;
                Health.ConsecutiveFailures++;
                Health.HealthScore = Math.Max(0.0, Health.HealthScore - 10.0);
            }

            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新性能指标
        /// </summary>
        public void UpdatePerformanceMetrics(TimeSpan responseTime, bool isSuccess)
        {
            Performance.LastMeasuredAt = DateTime.UtcNow;
            
            if (isSuccess)
            {
                Usage.TotalSuccessfulRequests++;
                Performance.SuccessRate = (double)Usage.TotalSuccessfulRequests / Usage.TotalRequests;
            }
            else
            {
                Usage.TotalFailedRequests++;
                Performance.ErrorRate = (double)Usage.TotalFailedRequests / Usage.TotalRequests;
            }

            // 更新响应时间统计
            if (Performance.MinResponseTime == TimeSpan.Zero || responseTime < Performance.MinResponseTime)
                Performance.MinResponseTime = responseTime;
            
            if (responseTime > Performance.MaxResponseTime)
                Performance.MaxResponseTime = responseTime;

            // 简单的移动平均
            Performance.AverageResponseTime = TimeSpan.FromMilliseconds(
                (Performance.AverageResponseTime.TotalMilliseconds * 0.9) + 
                (responseTime.TotalMilliseconds * 0.1)
            );

            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新使用统计
        /// </summary>
        public void UpdateUsageStatistics(long tokensUsed, decimal cost)
        {
            Usage.TotalRequests++;
            Usage.TotalTokensUsed += tokensUsed;
            Usage.TotalCost += cost;
            
            var today = DateTime.UtcNow.Date;
            if (!Usage.DailyUsage.ContainsKey(today))
            {
                Usage.DailyUsage[today] = new UsageDayData { Date = today };
            }
            
            Usage.DailyUsage[today].Requests++;
            Usage.DailyUsage[today].TokensUsed += tokensUsed;
            Usage.DailyUsage[today].Cost += cost;

            Usage.LastUpdated = DateTime.UtcNow;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 检查是否可用
        /// </summary>
        public bool IsAvailable()
        {
            return ServerInfo.IsActive && 
                   Health.IsHealthy && 
                   !Health.MaintenanceMode && 
                   !IsDeleted;
        }

        /// <summary>
        /// 获取服务权重（用于负载均衡）
        /// </summary>
        public double GetEffectiveWeight()
        {
            if (!IsAvailable()) return 0.0;
            
            // 基于健康评分和配置权重计算有效权重
            return ServerInfo.Weight * (Health.HealthScore / 100.0);
        }

        /// <summary>
        /// 克隆服务数据（深拷贝）
        /// </summary>
        public AITalkServiceData Clone()
        {
            // 这里应该实现深拷贝逻辑
            // 为了简化，这里只是创建新实例
            return new AITalkServiceData
            {
                Id = Guid.NewGuid(),
                ServerInfo = new ServerInfo
                {
                    ServerName = ServerInfo.ServerName + "_Clone",
                    ServerType = ServerInfo.ServerType,
                    BaseUrl = ServerInfo.BaseUrl,
                    // ... 其他属性
                },
                // ... 其他属性的拷贝
                CreatedAt = DateTime.UtcNow,
                LastUpdated = DateTime.UtcNow
            };
        }
    }
}
