using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models
{
    /// <summary>
    /// 服务负载信息
    /// </summary>
    public class ServiceLoadInfo
    {
        public Guid ServiceId { get; set; }
        public int ActiveConnections { get; set; }
        public int QueuedRequests { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double ThroughputPerSecond { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 故障转移事件
    /// </summary>
    public class FailoverEvent
    {
        public Guid EventId { get; set; } = Guid.NewGuid();
        public Guid FailedServiceId { get; set; }
        public Guid TargetServiceId { get; set; }
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
        public string Reason { get; set; } = string.Empty;
        public TimeSpan FailoverDuration { get; set; }
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 请求指标
    /// </summary>
    public class RequestMetrics
    {
        public Guid RequestId { get; set; }
        public DateTime StartTime { get; set; }
        public TimeSpan Duration { get; set; }
        public long RequestSize { get; set; }
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public Dictionary<string, object> Headers { get; set; } = new();
        public string UserAgent { get; set; } = string.Empty;
        public string ClientIp { get; set; } = string.Empty;
    }

    /// <summary>
    /// 响应指标
    /// </summary>
    public class ResponseMetrics
    {
        public Guid RequestId { get; set; }
        public DateTime ResponseTime { get; set; }
        public int StatusCode { get; set; }
        public long ResponseSize { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public Dictionary<string, object> Headers { get; set; } = new();
    }

    /// <summary>
    /// 实时指标
    /// </summary>
    public class RealTimeMetrics
    {
        public Guid ServiceId { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public int ActiveRequests { get; set; }
        public double RequestsPerSecond { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double ErrorRate { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public int QueueLength { get; set; }
        public Dictionary<string, double> CustomMetrics { get; set; } = new();
    }

    /// <summary>
    /// 告警配置
    /// </summary>
    public class AlertConfiguration
    {
        public Guid ConfigId { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<AlertRule> Rules { get; set; } = new();
        public List<string> NotificationChannels { get; set; } = new();
        public bool IsEnabled { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 告警规则
    /// </summary>
    public class AlertRule
    {
        public string MetricName { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty; // >, <, >=, <=, ==, !=
        public double Threshold { get; set; }
        public TimeSpan Duration { get; set; }
        public string Severity { get; set; } = "Warning";
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 趋势数据
    /// </summary>
    public class TrendData
    {
        public DateTime Timestamp { get; set; }
        public string MetricName { get; set; } = string.Empty;
        public double Value { get; set; }
        public string TrendDirection { get; set; } = string.Empty; // "up", "down", "stable"
        public double ChangePercentage { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 比较报告
    /// </summary>
    public class ComparisonReport
    {
        public List<Guid> ServiceIds { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan ComparisonPeriod { get; set; }
        public Dictionary<Guid, PerformanceMetrics> ServiceMetrics { get; set; } = new();
        public List<ComparisonInsight> Insights { get; set; } = new();
        public Dictionary<string, object> Summary { get; set; } = new();
    }

    /// <summary>
    /// 比较洞察
    /// </summary>
    public class ComparisonInsight
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Guid? BestPerformingService { get; set; }
        public Guid? WorstPerformingService { get; set; }
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// 服务配置
    /// </summary>
    public class ServiceConfiguration
    {
        public Guid ServiceId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
        public Dictionary<string, object> Settings { get; set; } = new();
        public List<string> EnabledFeatures { get; set; } = new();
        public Dictionary<string, object> Endpoints { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastModified { get; set; } = DateTime.UtcNow;
        public string CreatedBy { get; set; } = string.Empty;
        public string LastModifiedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 配置版本
    /// </summary>
    public class ConfigurationVersion
    {
        public int Version { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ServiceConfiguration Configuration { get; set; } = new();
        public string ChangeLog { get; set; } = string.Empty;
    }

    /// <summary>
    /// 配置变更
    /// </summary>
    public class ConfigurationChange
    {
        public Guid ChangeId { get; set; } = Guid.NewGuid();
        public string ChangeType { get; set; } = string.Empty; // "added", "modified", "removed"
        public string PropertyPath { get; set; } = string.Empty;
        public object? OldValue { get; set; }
        public object? NewValue { get; set; }
        public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
        public string ChangedBy { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 循环模式
    /// </summary>
    public class LoopPattern
    {
        public Guid PatternId { get; set; } = Guid.NewGuid();
        public List<Guid> TaskSequence { get; set; } = new();
        public int LoopCount { get; set; }
        public TimeSpan LoopDuration { get; set; }
        public DateTime FirstDetected { get; set; } = DateTime.UtcNow;
        public DateTime LastOccurred { get; set; } = DateTime.UtcNow;
        public string PatternType { get; set; } = string.Empty; // "simple", "complex", "infinite"
        public Dictionary<string, object> Characteristics { get; set; } = new();
    }

    /// <summary>
    /// 循环统计
    /// </summary>
    public class LoopStatistics
    {
        public int TotalLoopsDetected { get; set; }
        public int InfiniteLoopsDetected { get; set; }
        public int LoopsResolved { get; set; }
        public TimeSpan AverageLoopDuration { get; set; }
        public TimeSpan MaxLoopDuration { get; set; }
        public Dictionary<LoopBreakingStrategy, int> StrategyUsage { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 轮次结果
    /// </summary>
    public class RoundResult
    {
        public int RoundNumber { get; set; }
        public List<AITask> CompletedTasks { get; set; } = new();
        public List<AITask> FailedTasks { get; set; } = new();
        public List<AITask> GeneratedTasks { get; set; } = new();
        public object? RoundOutput { get; set; }
        public RoundMetrics RoundMetrics { get; set; } = new();
        public bool ShouldContinue { get; set; } = true;
        public string? NextRoundHint { get; set; }
        public bool UserInteractionRequired { get; set; } = false;
        public List<Exception> ErrorsEncountered { get; set; } = new();
        public List<string> WarningsGenerated { get; set; } = new();
        public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 轮次指标
    /// </summary>
    public class RoundMetrics
    {
        public TimeSpan Duration { get; set; }
        public int TasksExecuted { get; set; }
        public int TasksSucceeded { get; set; }
        public int TasksFailed { get; set; }
        public long TokensUsed { get; set; }
        public decimal Cost { get; set; }
        public double AverageTaskDuration { get; set; }
        public double SuccessRate { get; set; }
        public Dictionary<string, object> CustomMetrics { get; set; } = new();
    }

    /// <summary>
    /// 对话进度
    /// </summary>
    public class ConversationProgress
    {
        public Guid ConversationId { get; set; }
        public int CurrentRound { get; set; }
        public int MaxRounds { get; set; }
        public double ProgressPercentage { get; set; }
        public int CompletedTasks { get; set; }
        public int PendingTasks { get; set; }
        public int FailedTasks { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan? EstimatedRemainingTime { get; set; }
        public string CurrentPhase { get; set; } = string.Empty;
        public List<string> CompletedPhases { get; set; } = new();
        public List<string> RemainingPhases { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 中间结果
    /// </summary>
    public class IntermediateResult
    {
        public Guid ResultId { get; set; } = Guid.NewGuid();
        public int Round { get; set; }
        public Guid TaskId { get; set; }
        public object? Data { get; set; }
        public double ConfidenceScore { get; set; }
        public string ResultType { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 任务执行结果
    /// </summary>
    public class TaskExecutionResult
    {
        public Guid TaskId { get; set; }
        public bool IsSuccess { get; set; }
        public object? Output { get; set; }
        public Exception? Error { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public long TokensUsed { get; set; }
        public decimal Cost { get; set; }
        public Dictionary<string, object> Metrics { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 任务执行进度
    /// </summary>
    public class TaskExecutionProgress
    {
        public Guid TaskId { get; set; }
        public double ProgressPercentage { get; set; }
        public string CurrentStep { get; set; } = string.Empty;
        public string StatusMessage { get; set; } = string.Empty;
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan? EstimatedRemainingTime { get; set; }
        public Dictionary<string, object> StepData { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 聚合结果
    /// </summary>
    public class AggregatedResult
    {
        public Guid ResultId { get; set; } = Guid.NewGuid();
        public List<TaskExecutionResult> SourceResults { get; set; } = new();
        public object? CombinedOutput { get; set; }
        public double OverallConfidence { get; set; }
        public string AggregationMethod { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 结果冲突
    /// </summary>
    public class ResultConflict
    {
        public Guid ConflictId { get; set; } = Guid.NewGuid();
        public ResultConflictType ConflictType { get; set; }
        public List<TaskExecutionResult> ConflictingResults { get; set; } = new();
        public string Description { get; set; } = string.Empty;
        public double Severity { get; set; }
        public Dictionary<string, object> ConflictDetails { get; set; } = new();
        public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 冲突解决结果
    /// </summary>
    public class ConflictResolutionResult
    {
        public Guid ResolutionId { get; set; } = Guid.NewGuid();
        public Guid ConflictId { get; set; }
        public ConflictResolutionStrategy Strategy { get; set; }
        public object? ResolvedValue { get; set; }
        public string Reasoning { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public DateTime ResolvedAt { get; set; } = DateTime.UtcNow;
    }
}
