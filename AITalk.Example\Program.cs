using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using AITalk.Core.Models;
using AITalk.Core.Models.ConversationData;
using AITalk.Core.Models.ServiceData;
using AITalk.Core.Enums;

namespace AITalk.Example
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== AITalk Framework Demo ===");
            Console.WriteLine();

            // 演示创建服务数据
            await DemoServiceDataCreation();

            Console.WriteLine();

            // 演示创建对话数据
            await DemoConversationDataCreation();

            Console.WriteLine();

            // 演示任务创建和管理
            await DemoTaskManagement();

            Console.WriteLine();

            // 测试真实的DeepSeek API调用
            await TestDeepSeekAPI();

            Console.WriteLine();
            Console.WriteLine("Demo completed. Press any key to exit...");
            Console.ReadKey();
        }

        static async Task DemoServiceDataCreation()
        {
            Console.WriteLine("--- Service Data Creation Demo (New Architecture) ---");

            // 创建DeepSeek服务配置 - 使用新的详细数据结构
            var serviceData = new AITalkServiceData
            {
                ServiceName = "DeepSeek API Service",
                Description = "DeepSeek AI服务，支持chat和reasoner模型",
                ProviderType = ProviderType.OpenAI, // DeepSeek兼容OpenAI格式

                // 网络连接信息
                PrimaryIpAddress = "************", // DeepSeek的一个IP
                ServerPort = 443,
                BaseUrl = "https://api.deepseek.com/v1",
                ApiVersion = "v1",
                ServerRegion = "Global",
                DataCenter = "CloudFlare",

                // API密钥和认证
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22",
                AuthType = AuthenticationType.ApiKey,
                UseSSL = true,
                ValidateSSLCertificate = true,

                // 性能配置
                MaxConcurrentConnections = 10,

                // 初始化健康状态
                IsHealthy = true,
                HealthScore = 100.0,
                AvailabilityPercentage = 99.9
            };

            Console.WriteLine($"✅ Created service: {serviceData.ServiceName}");
            Console.WriteLine($"   Service ID: {serviceData.Id}");
            Console.WriteLine($"   Provider: {serviceData.ProviderType}");
            Console.WriteLine($"   Base URL: {serviceData.BaseUrl}");
            Console.WriteLine($"   Primary IP: {serviceData.PrimaryIpAddress}:{serviceData.ServerPort}");
            Console.WriteLine($"   API Key: {serviceData.PrimaryApiKey.Substring(0, 10)}...");
            Console.WriteLine($"   Health Score: {serviceData.HealthScore}%");
            Console.WriteLine($"   SSL Enabled: {serviceData.UseSSL}");

            // 模拟性能指标更新
            serviceData.UpdatePerformanceMetrics(TimeSpan.FromMilliseconds(1500), true);
            Console.WriteLine($"   Average Response Time: {serviceData.AverageResponseTimeMs:F1}ms");
            Console.WriteLine($"   Success Rate: {serviceData.SuccessRate:F1}%");

            await Task.CompletedTask;
        }

        static async Task DemoConversationDataCreation()
        {
            Console.WriteLine("--- Conversation Data Creation Demo (New Architecture) ---");

            // 创建对话数据 - 使用新的AITalkData结构
            var conversationData = new AITalkData
            {
                ConversationName = "AI Assistant Multi-Round Conversation",
                UserId = "user123",
                ConversationType = ConversationType.MultiRound,
                Status = ConversationStatus.Created,

                // 多轮调度配置
                MaxRounds = 10,
                MaxMessages = 1000,
                Timeout = TimeSpan.FromMinutes(30),
                EnableAutoSave = true,
                AutoSaveInterval = TimeSpan.FromMinutes(5),

                // 自循环控制
                MaxLoopIterations = 5,
                LoopBreakingStrategy = LoopBreakingStrategy.LimitIterations,

                // 任务调度配置
                MaxConcurrentTasks = 3
            };

            Console.WriteLine($"✅ Created conversation: {conversationData.ConversationName}");
            Console.WriteLine($"   Conversation ID: {conversationData.ConversationId}");
            Console.WriteLine($"   User ID: {conversationData.UserId}");
            Console.WriteLine($"   Type: {conversationData.ConversationType}");
            Console.WriteLine($"   Max Rounds: {conversationData.MaxRounds}");
            Console.WriteLine($"   Max Loop Iterations: {conversationData.MaxLoopIterations}");
            Console.WriteLine($"   Max Concurrent Tasks: {conversationData.MaxConcurrentTasks}");

            // 添加一些示例消息
            var userMessage = new ConversationMessage
            {
                Role = MessageRole.User,
                Content = "Hello, I need help with planning a trip to Japan.",
                ContentType = MessageContentType.Text
            };

            conversationData.AddMessage(userMessage);
            Console.WriteLine($"   Added user message: {userMessage.Content}");

            var assistantMessage = new ConversationMessage
            {
                Role = MessageRole.Assistant,
                Content = "I'd be happy to help you plan your trip to Japan! To provide the best recommendations, could you tell me more about your preferences?",
                ContentType = MessageContentType.Text,
                TokenCount = 25
            };

            conversationData.AddMessage(assistantMessage);
            Console.WriteLine($"   Added assistant message: {assistantMessage.Content}");

            Console.WriteLine($"   Total messages: {conversationData.Messages.Count}");
            Console.WriteLine($"   Conversation status: {conversationData.Status}");
            Console.WriteLine($"   Is Active: {conversationData.IsActive()}");

            await Task.CompletedTask;
        }

        static async Task DemoTaskManagement()
        {
            Console.WriteLine("--- Task Management Demo ---");

            // 创建一些示例任务
            var initialTask = new AITask
            {
                TaskType = TaskType.InitialAnalysis,
                TaskName = "Analyze User Request",
                Description = "Analyze the user's trip planning request to understand requirements",
                Priority = 1,
                Round = 1,
                RequiresExternalService = true,
                Configuration = new TaskConfiguration
                {
                    Timeout = TimeSpan.FromMinutes(2),
                    MaxRetries = 3,
                    EnableLogging = true
                }
            };

            var gatheringTask = new AITask
            {
                TaskType = TaskType.InformationGathering,
                TaskName = "Gather Travel Information",
                Description = "Collect information about destinations, activities, and accommodations in Japan",
                Priority = 2,
                Round = 1,
                Dependencies = new List<Guid> { initialTask.TaskId },
                RequiresExternalService = true
            };

            var synthesisTask = new AITask
            {
                TaskType = TaskType.FinalSynthesis,
                TaskName = "Create Travel Plan",
                Description = "Synthesize gathered information into a comprehensive travel plan",
                Priority = 3,
                Round = 2,
                Dependencies = new List<Guid> { gatheringTask.TaskId },
                RequiresExternalService = true
            };

            Console.WriteLine($"Created task: {initialTask.TaskName} (ID: {initialTask.TaskId})");
            Console.WriteLine($"Created task: {gatheringTask.TaskName} (ID: {gatheringTask.TaskId})");
            Console.WriteLine($"Created task: {synthesisTask.TaskName} (ID: {synthesisTask.TaskId})");

            // 模拟任务执行
            Console.WriteLine("\nSimulating task execution...");

            // 开始第一个任务
            initialTask.Start();
            Console.WriteLine($"Started: {initialTask.TaskName} - Status: {initialTask.Status}");

            // 模拟任务完成
            await Task.Delay(100); // 模拟处理时间
            initialTask.Complete(new { analysis = "User wants to visit Japan for cultural experiences" });
            Console.WriteLine($"Completed: {initialTask.TaskName} - Status: {initialTask.Status}");
            Console.WriteLine($"Duration: {initialTask.ActualDuration}");

            // 检查依赖关系
            var allTasks = new Dictionary<Guid, AITask>
            {
                { initialTask.TaskId, initialTask },
                { gatheringTask.TaskId, gatheringTask },
                { synthesisTask.TaskId, synthesisTask }
            };

            bool canStartGathering = gatheringTask.AreDependenciesSatisfied(allTasks);
            Console.WriteLine($"Can start gathering task: {canStartGathering}");

            if (canStartGathering)
            {
                gatheringTask.Start();
                Console.WriteLine($"Started: {gatheringTask.TaskName} - Status: {gatheringTask.Status}");

                await Task.Delay(150); // 模拟处理时间
                gatheringTask.Complete(new { destinations = new[] { "Tokyo", "Kyoto", "Osaka" } });
                Console.WriteLine($"Completed: {gatheringTask.TaskName} - Status: {gatheringTask.Status}");
            }

            // 检查最终任务的依赖
            bool canStartSynthesis = synthesisTask.AreDependenciesSatisfied(allTasks);
            Console.WriteLine($"Can start synthesis task: {canStartSynthesis}");

            if (canStartSynthesis)
            {
                synthesisTask.Start();
                Console.WriteLine($"Started: {synthesisTask.TaskName} - Status: {synthesisTask.Status}");

                await Task.Delay(200); // 模拟处理时间
                synthesisTask.Complete(new { plan = "7-day cultural tour of Japan visiting Tokyo, Kyoto, and Osaka" });
                Console.WriteLine($"Completed: {synthesisTask.TaskName} - Status: {synthesisTask.Status}");
            }

            Console.WriteLine("\nTask execution simulation completed!");
            Console.WriteLine($"Total tasks completed: 3");
            Console.WriteLine($"All tasks successful: {initialTask.Status == AITalk.Core.Enums.TaskStatus.Completed && gatheringTask.Status == AITalk.Core.Enums.TaskStatus.Completed && synthesisTask.Status == AITalk.Core.Enums.TaskStatus.Completed}");

            await Task.CompletedTask;
        }

        static async Task TestDeepSeekAPI()
        {
            Console.WriteLine("--- DeepSeek API Test (New Architecture) ---");

            // 创建DeepSeek服务配置 - 使用新的数据结构
            var deepSeekService = new AITalkServiceData
            {
                ServiceName = "DeepSeek API",
                Description = "DeepSeek AI服务测试",
                ProviderType = ProviderType.OpenAI, // DeepSeek兼容OpenAI格式
                BaseUrl = "https://api.deepseek.com/v1",
                ServerRegion = "Global",
                PrimaryApiKey = "sk-235870fcddb84a308771480cbac2ad22",
                AuthType = AuthenticationType.ApiKey,
                UseSSL = true
            };

            Console.WriteLine($"✅ Created DeepSeek service: {deepSeekService.ServiceName}");
            Console.WriteLine($"   Base URL: {deepSeekService.BaseUrl}");
            Console.WriteLine($"   API Key: {deepSeekService.PrimaryApiKey[..10]}...");

            // 测试两个模型
            await TestDeepSeekModel(deepSeekService, "deepseek-chat", "你好，请简单介绍一下你自己。");
            Console.WriteLine();
            await TestDeepSeekModel(deepSeekService, "deepseek-reasoner", "请解释一下什么是人工智能，并分析其发展趋势。");
        }

        static async Task TestDeepSeekModel(AITalkServiceData service, string model, string prompt)
        {
            Console.WriteLine($"Testing model: {model}");
            Console.WriteLine($"Prompt: {prompt}");

            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {service.PrimaryApiKey}");

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 500,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var startTime = DateTime.UtcNow;
                var response = await httpClient.PostAsync($"{service.BaseUrl}/chat/completions", content);
                var duration = DateTime.UtcNow - startTime;

                Console.WriteLine($"Response Status: {response.StatusCode}");
                Console.WriteLine($"Response Time: {duration.TotalMilliseconds:F0}ms");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var responseJson = JsonDocument.Parse(responseContent);

                    if (responseJson.RootElement.TryGetProperty("choices", out var choices) &&
                        choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var messageContent))
                        {
                            Console.WriteLine($"Response: {messageContent.GetString()}");
                        }
                    }

                    // 更新服务性能指标
                    service.UpdatePerformanceMetrics(duration, true);
                    service.UpdateHealth(true, $"Successfully called {model}");

                    Console.WriteLine($"✅ API call successful for {model}");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"❌ API call failed: {errorContent}");

                    service.UpdatePerformanceMetrics(duration, false);
                    service.UpdateHealth(false, $"Failed to call {model}: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Exception occurred: {ex.Message}");
                service.UpdateHealth(false, $"Exception calling {model}: {ex.Message}");
            }
        }
    }
}
