﻿using System;
using System.Threading.Tasks;
using AITalk.Core.Models;
using AITalk.Core.Models.ConversationData;
using AITalk.Core.Models.ServiceData;
using AITalk.Core.Enums;

namespace AITalk.Example
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== AITalk Framework Demo ===");
            Console.WriteLine();

            // 演示创建服务数据
            await DemoServiceDataCreation();

            Console.WriteLine();

            // 演示创建对话数据
            await DemoConversationDataCreation();

            Console.WriteLine();

            // 演示任务创建和管理
            await DemoTaskManagement();

            Console.WriteLine();
            Console.WriteLine("Demo completed. Press any key to exit...");
            Console.ReadKey();
        }

        static async Task DemoServiceDataCreation()
        {
            Console.WriteLine("--- Service Data Creation Demo ---");

            // 创建一个OpenAI服务配置
            var serviceData = new AITalkServiceData
            {
                ServerInfo = new ServerInfo
                {
                    ServerName = "OpenAI GPT-4",
                    ServerType = ServiceType.OpenAI,
                    BaseUrl = "https://api.openai.com/v1",
                    Region = "US-East",
                    SupportedModels = new List<string> { "gpt-4", "gpt-4-turbo", "gpt-3.5-turbo" }
                },
                Authentication = new AuthenticationInfo
                {
                    AuthType = AuthenticationType.ApiKey,
                    ApiKey = "sk-your-api-key-here" // 实际使用时应该加密存储
                },
                Capabilities = new ServiceCapabilities
                {
                    SupportsStreaming = true,
                    SupportsFunctionCalling = true,
                    SupportsVision = true,
                    MaxContextLength = 128000,
                    MaxOutputLength = 4096
                },
                Pricing = new PricingInfo
                {
                    PricingModel = "PayPerUse",
                    InputTokenPrice = 0.00003m,
                    OutputTokenPrice = 0.00006m,
                    Currency = "USD"
                }
            };

            Console.WriteLine($"Created service: {serviceData.ServerInfo.ServerName}");
            Console.WriteLine($"Service ID: {serviceData.Id}");
            Console.WriteLine($"Base URL: {serviceData.ServerInfo.BaseUrl}");
            Console.WriteLine($"Supports Streaming: {serviceData.Capabilities.SupportsStreaming}");
            Console.WriteLine($"Max Context Length: {serviceData.Capabilities.MaxContextLength}");

            // 模拟健康检查
            serviceData.UpdateHealth(true, "Service is healthy and ready");
            Console.WriteLine($"Health Status: {serviceData.Health.StatusMessage}");
            Console.WriteLine($"Health Score: {serviceData.Health.HealthScore}");

            await Task.CompletedTask;
        }

        static async Task DemoConversationDataCreation()
        {
            Console.WriteLine("--- Conversation Data Creation Demo ---");

            // 创建对话数据
            var conversationData = new AITalkData
            {
                ConversationInfo = new ConversationInfo
                {
                    Title = "AI Assistant Conversation",
                    Description = "A multi-round conversation with AI assistant",
                    ConversationType = ConversationType.MultiRound,
                    Priority = ConversationPriority.Normal,
                    UserId = "user123",
                    SessionId = "session456"
                },
                Configuration = new ConversationConfiguration
                {
                    MaxRounds = 10,
                    MaxDuration = TimeSpan.FromMinutes(30),
                    EnableAutoSave = true,
                    EnableQualityAssessment = true
                }
            };

            Console.WriteLine($"Created conversation: {conversationData.ConversationInfo.Title}");
            Console.WriteLine($"Conversation ID: {conversationData.ConversationInfo.ConversationId}");
            Console.WriteLine($"User ID: {conversationData.ConversationInfo.UserId}");
            Console.WriteLine($"Max Rounds: {conversationData.Configuration.MaxRounds}");

            // 添加一些示例消息
            var userMessage = new ConversationMessage
            {
                Role = MessageRole.User,
                Content = "Hello, I need help with planning a trip to Japan.",
                ContentType = MessageContentType.Text
            };

            conversationData.AddMessage(userMessage);
            Console.WriteLine($"Added user message: {userMessage.Content}");

            var assistantMessage = new ConversationMessage
            {
                Role = MessageRole.Assistant,
                Content = "I'd be happy to help you plan your trip to Japan! To provide the best recommendations, could you tell me more about your preferences?",
                ContentType = MessageContentType.Text,
                TokenCount = 25
            };

            conversationData.AddMessage(assistantMessage);
            Console.WriteLine($"Added assistant message: {assistantMessage.Content}");

            Console.WriteLine($"Total messages: {conversationData.MessageHistory.TotalMessageCount}");
            Console.WriteLine($"Conversation status: {conversationData.ConversationInfo.Status}");

            await Task.CompletedTask;
        }

        static async Task DemoTaskManagement()
        {
            Console.WriteLine("--- Task Management Demo ---");

            // 创建一些示例任务
            var initialTask = new AITask
            {
                TaskType = TaskType.InitialAnalysis,
                TaskName = "Analyze User Request",
                Description = "Analyze the user's trip planning request to understand requirements",
                Priority = 1,
                Round = 1,
                RequiresExternalService = true,
                Configuration = new TaskConfiguration
                {
                    Timeout = TimeSpan.FromMinutes(2),
                    MaxRetries = 3,
                    EnableLogging = true
                }
            };

            var gatheringTask = new AITask
            {
                TaskType = TaskType.InformationGathering,
                TaskName = "Gather Travel Information",
                Description = "Collect information about destinations, activities, and accommodations in Japan",
                Priority = 2,
                Round = 1,
                Dependencies = new List<Guid> { initialTask.TaskId },
                RequiresExternalService = true
            };

            var synthesisTask = new AITask
            {
                TaskType = TaskType.FinalSynthesis,
                TaskName = "Create Travel Plan",
                Description = "Synthesize gathered information into a comprehensive travel plan",
                Priority = 3,
                Round = 2,
                Dependencies = new List<Guid> { gatheringTask.TaskId },
                RequiresExternalService = true
            };

            Console.WriteLine($"Created task: {initialTask.TaskName} (ID: {initialTask.TaskId})");
            Console.WriteLine($"Created task: {gatheringTask.TaskName} (ID: {gatheringTask.TaskId})");
            Console.WriteLine($"Created task: {synthesisTask.TaskName} (ID: {synthesisTask.TaskId})");

            // 模拟任务执行
            Console.WriteLine("\nSimulating task execution...");

            // 开始第一个任务
            initialTask.Start();
            Console.WriteLine($"Started: {initialTask.TaskName} - Status: {initialTask.Status}");

            // 模拟任务完成
            await Task.Delay(100); // 模拟处理时间
            initialTask.Complete(new { analysis = "User wants to visit Japan for cultural experiences" });
            Console.WriteLine($"Completed: {initialTask.TaskName} - Status: {initialTask.Status}");
            Console.WriteLine($"Duration: {initialTask.ActualDuration}");

            // 检查依赖关系
            var allTasks = new Dictionary<Guid, AITask>
            {
                { initialTask.TaskId, initialTask },
                { gatheringTask.TaskId, gatheringTask },
                { synthesisTask.TaskId, synthesisTask }
            };

            bool canStartGathering = gatheringTask.AreDependenciesSatisfied(allTasks);
            Console.WriteLine($"Can start gathering task: {canStartGathering}");

            if (canStartGathering)
            {
                gatheringTask.Start();
                Console.WriteLine($"Started: {gatheringTask.TaskName} - Status: {gatheringTask.Status}");

                await Task.Delay(150); // 模拟处理时间
                gatheringTask.Complete(new { destinations = new[] { "Tokyo", "Kyoto", "Osaka" } });
                Console.WriteLine($"Completed: {gatheringTask.TaskName} - Status: {gatheringTask.Status}");
            }

            // 检查最终任务的依赖
            bool canStartSynthesis = synthesisTask.AreDependenciesSatisfied(allTasks);
            Console.WriteLine($"Can start synthesis task: {canStartSynthesis}");

            if (canStartSynthesis)
            {
                synthesisTask.Start();
                Console.WriteLine($"Started: {synthesisTask.TaskName} - Status: {synthesisTask.Status}");

                await Task.Delay(200); // 模拟处理时间
                synthesisTask.Complete(new { plan = "7-day cultural tour of Japan visiting Tokyo, Kyoto, and Osaka" });
                Console.WriteLine($"Completed: {synthesisTask.TaskName} - Status: {synthesisTask.Status}");
            }

            Console.WriteLine("\nTask execution simulation completed!");
            Console.WriteLine($"Total tasks completed: 3");
            Console.WriteLine($"All tasks successful: {initialTask.Status == AITalk.Core.Enums.TaskStatus.Completed && gatheringTask.Status == AITalk.Core.Enums.TaskStatus.Completed && synthesisTask.Status == AITalk.Core.Enums.TaskStatus.Completed}");

            await Task.CompletedTask;
        }
    }
}
