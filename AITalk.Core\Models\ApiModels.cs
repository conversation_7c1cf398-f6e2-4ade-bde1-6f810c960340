using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models
{
    /// <summary>
    /// API请求基类
    /// </summary>
    public class ApiRequest
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = "POST";
        public Dictionary<string, string> Headers { get; set; } = new();
        public object? Body { get; set; }
        public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);
        public Dictionary<string, object> Parameters { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public Guid RequestId { get; set; } = Guid.NewGuid();
    }

    /// <summary>
    /// API响应基类
    /// </summary>
    public class ApiResponse<T>
    {
        public bool IsSuccess { get; set; }
        public T? Data { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string ErrorCode { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public Dictionary<string, string> Headers { get; set; } = new();
        public TimeSpan ResponseTime { get; set; }
        public DateTime ReceivedAt { get; set; } = DateTime.UtcNow;
        public Guid RequestId { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 聊天完成请求
    /// </summary>
    public class ChatCompletionRequest
    {
        public string Model { get; set; } = string.Empty;
        public List<ChatMessage> Messages { get; set; } = new();
        public double? Temperature { get; set; }
        public int? MaxTokens { get; set; }
        public double? TopP { get; set; }
        public int? N { get; set; }
        public bool Stream { get; set; } = false;
        public List<string>? Stop { get; set; }
        public double? PresencePenalty { get; set; }
        public double? FrequencyPenalty { get; set; }
        public Dictionary<string, object>? LogitBias { get; set; }
        public string? User { get; set; }
        public List<Function>? Functions { get; set; }
        public object? FunctionCall { get; set; }
        public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    }

    /// <summary>
    /// 聊天消息
    /// </summary>
    public class ChatMessage
    {
        public string Role { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string? Name { get; set; }
        public object? FunctionCall { get; set; }
        public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    }

    /// <summary>
    /// 函数定义
    /// </summary>
    public class Function
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public object Parameters { get; set; } = new();
    }

    /// <summary>
    /// 聊天完成响应
    /// </summary>
    public class ChatCompletionResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Object { get; set; } = string.Empty;
        public long Created { get; set; }
        public string Model { get; set; } = string.Empty;
        public List<Choice> Choices { get; set; } = new();
        public Usage? Usage { get; set; }
        public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    }

    /// <summary>
    /// 选择项
    /// </summary>
    public class Choice
    {
        public int Index { get; set; }
        public ChatMessage? Message { get; set; }
        public object? Delta { get; set; }
        public string? FinishReason { get; set; }
        public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    }

    /// <summary>
    /// 使用情况
    /// </summary>
    public class Usage
    {
        public int PromptTokens { get; set; }
        public int CompletionTokens { get; set; }
        public int TotalTokens { get; set; }
        public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    }

    /// <summary>
    /// 流式请求
    /// </summary>
    public class StreamingRequest : ChatCompletionRequest
    {
        public new bool Stream { get; set; } = true;
        public TimeSpan StreamTimeout { get; set; } = TimeSpan.FromMinutes(5);
        public int BufferSize { get; set; } = 8192;
    }

    /// <summary>
    /// 流式响应
    /// </summary>
    public class StreamingResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Object { get; set; } = string.Empty;
        public long Created { get; set; }
        public string Model { get; set; } = string.Empty;
        public List<Choice> Choices { get; set; } = new();
        public bool IsComplete { get; set; } = false;
        public string? Error { get; set; }
        public DateTime ReceivedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// 健康检查结果
    /// </summary>
    public class HealthCheckResult
    {
        public bool IsHealthy { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan ResponseTime { get; set; }
        public Dictionary<string, object> Details { get; set; } = new();
        public List<string> Issues { get; set; } = new();
        public double HealthScore { get; set; } = 0.0;
    }

    /// <summary>
    /// 性能报告
    /// </summary>
    public class PerformanceReport
    {
        public Guid ServiceId { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan ReportPeriod { get; set; }
        public PerformanceMetrics Metrics { get; set; } = new();
        public List<PerformanceIssue> Issues { get; set; } = new();
        public List<PerformanceRecommendation> Recommendations { get; set; } = new();
        public Dictionary<string, object> RawData { get; set; } = new();
    }

    /// <summary>
    /// 性能问题
    /// </summary>
    public class PerformanceIssue
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// 性能建议
    /// </summary>
    public class PerformanceRecommendation
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public int Priority { get; set; } = 0;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 认证结果
    /// </summary>
    public class AuthenticationResult
    {
        public bool IsSuccess { get; set; }
        public string AccessToken { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public Dictionary<string, object> Claims { get; set; } = new();
    }

    /// <summary>
    /// 令牌刷新结果
    /// </summary>
    public class TokenRefreshResult
    {
        public bool IsSuccess { get; set; }
        public string NewAccessToken { get; set; } = string.Empty;
        public string NewRefreshToken { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 加密凭据
    /// </summary>
    public class EncryptedCredentials
    {
        public string EncryptedData { get; set; } = string.Empty;
        public string EncryptionMethod { get; set; } = string.Empty;
        public string Salt { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 密钥轮换结果
    /// </summary>
    public class KeyRotationResult
    {
        public bool IsSuccess { get; set; }
        public string NewApiKey { get; set; } = string.Empty;
        public string OldApiKey { get; set; } = string.Empty;
        public DateTime RotatedAt { get; set; } = DateTime.UtcNow;
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 安全事件
    /// </summary>
    public class SecurityEvent
    {
        public Guid EventId { get; set; } = Guid.NewGuid();
        public string EventType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
        public string Severity { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// 异常告警
    /// </summary>
    public class AnomalyAlert
    {
        public Guid AlertId { get; set; } = Guid.NewGuid();
        public string AnomalyType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Severity { get; set; } = 0.0;
        public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Metrics { get; set; } = new();
        public List<string> RecommendedActions { get; set; } = new();
    }

    /// <summary>
    /// 证书验证结果
    /// </summary>
    public class CertificateValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public string Issuer { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public List<string> Issues { get; set; } = new();
    }

    /// <summary>
    /// 安全策略
    /// </summary>
    public class SecurityPolicy
    {
        public string PolicyName { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
        public Dictionary<string, object> Rules { get; set; } = new();
        public List<string> AllowedOperations { get; set; } = new();
        public List<string> DeniedOperations { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ExpiresAt { get; set; }
    }
}
