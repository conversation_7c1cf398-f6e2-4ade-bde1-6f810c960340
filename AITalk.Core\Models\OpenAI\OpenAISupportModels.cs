using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.OpenAI
{
    /// <summary>
    /// 完成响应
    /// </summary>
    public class CompletionResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Object { get; set; } = "text_completion";
        public long Created { get; set; }
        public string Model { get; set; } = string.Empty;
        public List<CompletionChoice> Choices { get; set; } = new();
        public Usage? Usage { get; set; }
        public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    }

    /// <summary>
    /// 完成选择
    /// </summary>
    public class CompletionChoice
    {
        public string Text { get; set; } = string.Empty;
        public int Index { get; set; }
        public double? LogProbs { get; set; }
        public string? FinishReason { get; set; }
    }

    /// <summary>
    /// 嵌入响应
    /// </summary>
    public class EmbeddingResponse
    {
        public string Object { get; set; } = "list";
        public List<EmbeddingData> Data { get; set; } = new();
        public string Model { get; set; } = string.Empty;
        public Usage? Usage { get; set; }
        public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    }

    /// <summary>
    /// 嵌入数据
    /// </summary>
    public class EmbeddingData
    {
        public string Object { get; set; } = "embedding";
        public List<float> Embedding { get; set; } = new();
        public int Index { get; set; }
    }

    /// <summary>
    /// 模型能力
    /// </summary>
    public class ModelCapabilities
    {
        public string ModelId { get; set; } = string.Empty;
        public int MaxContextLength { get; set; }
        public int MaxOutputTokens { get; set; }
        public bool SupportsStreaming { get; set; } = false;
        public bool SupportsFunctionCalling { get; set; } = false;
        public bool SupportsVision { get; set; } = false;
        public bool SupportsAudio { get; set; } = false;
        public bool SupportsImageGeneration { get; set; } = false;
        public bool SupportsEmbeddings { get; set; } = false;
        public bool SupportsFineTuning { get; set; } = false;
        public List<string> SupportedLanguages { get; set; } = new();
        public List<string> SupportedFormats { get; set; } = new();
        public (double Min, double Max) TemperatureRange { get; set; } = (0.0, 2.0);
        public (double Min, double Max) TopPRange { get; set; } = (0.0, 1.0);
        public Dictionary<string, object> AdditionalCapabilities { get; set; } = new();
    }

    /// <summary>
    /// 模型限制
    /// </summary>
    public class ModelLimits
    {
        public string ModelId { get; set; } = string.Empty;
        public int MaxInputTokens { get; set; }
        public int MaxOutputTokens { get; set; }
        public int MaxRequestsPerMinute { get; set; }
        public int MaxTokensPerMinute { get; set; }
        public int MaxConcurrentRequests { get; set; }
        public long MaxFileSize { get; set; }
        public int MaxFilesPerRequest { get; set; }
        public TimeSpan MaxProcessingTime { get; set; }
        public Dictionary<string, object> AdditionalLimits { get; set; } = new();
    }

    /// <summary>
    /// 模型匹配
    /// </summary>
    public class ModelMatch
    {
        public ModelInfo Model { get; set; } = new();
        public double MatchScore { get; set; }
        public List<string> MatchedRequirements { get; set; } = new();
        public List<string> UnmatchedRequirements { get; set; } = new();
        public string MatchReason { get; set; } = string.Empty;
        public Dictionary<string, object> MatchDetails { get; set; } = new();
    }

    /// <summary>
    /// 模型需求
    /// </summary>
    public class ModelRequirements
    {
        public TaskType TaskType { get; set; }
        public int MinContextLength { get; set; } = 0;
        public int MinOutputTokens { get; set; } = 0;
        public bool RequiresStreaming { get; set; } = false;
        public bool RequiresFunctionCalling { get; set; } = false;
        public bool RequiresVision { get; set; } = false;
        public bool RequiresAudio { get; set; } = false;
        public List<string> RequiredLanguages { get; set; } = new();
        public decimal MaxCostPerToken { get; set; } = decimal.MaxValue;
        public TimeSpan MaxResponseTime { get; set; } = TimeSpan.MaxValue;
        public List<string> PreferredProviders { get; set; } = new();
        public Dictionary<string, object> CustomRequirements { get; set; } = new();
    }

    /// <summary>
    /// 模型性能
    /// </summary>
    public class ModelPerformance
    {
        public string ModelId { get; set; } = string.Empty;
        public ProviderType Provider { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double ThroughputPerSecond { get; set; }
        public double SuccessRate { get; set; }
        public double ErrorRate { get; set; }
        public double QualityScore { get; set; }
        public decimal AverageCostPerRequest { get; set; }
        public DateTime LastMeasured { get; set; } = DateTime.UtcNow;
        public Dictionary<string, double> DetailedMetrics { get; set; } = new();
    }

    /// <summary>
    /// 模型推荐
    /// </summary>
    public class ModelRecommendation
    {
        public ModelInfo Model { get; set; } = new();
        public double RecommendationScore { get; set; }
        public string RecommendationReason { get; set; } = string.Empty;
        public List<string> Pros { get; set; } = new();
        public List<string> Cons { get; set; } = new();
        public decimal EstimatedCost { get; set; }
        public TimeSpan EstimatedResponseTime { get; set; }
        public Dictionary<string, object> RecommendationDetails { get; set; } = new();
    }

    /// <summary>
    /// 端点文档
    /// </summary>
    public class EndpointDocumentation
    {
        public string EndpointPath { get; set; } = string.Empty;
        public List<string> SupportedMethods { get; set; } = new();
        public string Description { get; set; } = string.Empty;
        public List<EndpointParameter> Parameters { get; set; } = new();
        public List<EndpointExample> Examples { get; set; } = new();
        public List<string> RequiredHeaders { get; set; } = new();
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }

    /// <summary>
    /// 端点参数
    /// </summary>
    public class EndpointParameter
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool IsRequired { get; set; } = false;
        public string Description { get; set; } = string.Empty;
        public object? DefaultValue { get; set; }
        public List<object> AllowedValues { get; set; } = new();
    }

    /// <summary>
    /// 端点示例
    /// </summary>
    public class EndpointExample
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public object RequestExample { get; set; } = new();
        public object ResponseExample { get; set; } = new();
    }

    /// <summary>
    /// 流式错误
    /// </summary>
    public class StreamingError
    {
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> ErrorDetails { get; set; } = new();
    }

    /// <summary>
    /// 重试指令
    /// </summary>
    public class RetryInstruction
    {
        public bool ShouldRetry { get; set; } = false;
        public TimeSpan RetryAfter { get; set; }
        public int MaxRetries { get; set; } = 3;
        public RetryStrategyType Strategy { get; set; } = RetryStrategyType.Exponential;
        public string Reason { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 速率限制错误
    /// </summary>
    public class RateLimitError
    {
        public string ErrorCode { get; set; } = "rate_limit_exceeded";
        public string ErrorMessage { get; set; } = string.Empty;
        public int RemainingRequests { get; set; } = 0;
        public int RemainingTokens { get; set; } = 0;
        public DateTime ResetTime { get; set; }
        public TimeSpan RetryAfter { get; set; }
        public string LimitType { get; set; } = string.Empty;
    }

    /// <summary>
    /// 认证错误
    /// </summary>
    public class AuthError
    {
        public string ErrorCode { get; set; } = "authentication_failed";
        public string ErrorMessage { get; set; } = string.Empty;
        public string AuthMethod { get; set; } = string.Empty;
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> ErrorDetails { get; set; } = new();
    }

    /// <summary>
    /// 配额错误
    /// </summary>
    public class QuotaError
    {
        public string ErrorCode { get; set; } = "quota_exceeded";
        public string ErrorMessage { get; set; } = string.Empty;
        public long QuotaLimit { get; set; }
        public long QuotaUsed { get; set; }
        public DateTime QuotaResetDate { get; set; }
        public string QuotaType { get; set; } = string.Empty; // "requests", "tokens", "cost"
    }

    /// <summary>
    /// 错误报告
    /// </summary>
    public class ErrorReport
    {
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan ReportPeriod { get; set; }
        public int TotalErrors { get; set; }
        public Dictionary<string, int> ErrorsByType { get; set; } = new();
        public Dictionary<ProviderType, int> ErrorsByProvider { get; set; } = new();
        public List<OpenAIError> TopErrors { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public Dictionary<string, object> Statistics { get; set; } = new();
    }

    /// <summary>
    /// 配额信息
    /// </summary>
    public class QuotaInfo
    {
        public long TotalQuota { get; set; }
        public long UsedQuota { get; set; }
        public long RemainingQuota { get; set; }
        public double UsagePercentage { get; set; }
        public DateTime QuotaResetDate { get; set; }
        public string QuotaType { get; set; } = string.Empty;
        public Dictionary<string, long> DetailedUsage { get; set; } = new();
    }

    /// <summary>
    /// 速率限制头信息
    /// </summary>
    public class RateLimitHeaders
    {
        public int? RemainingRequests { get; set; }
        public int? RemainingTokens { get; set; }
        public DateTime? ResetTime { get; set; }
        public TimeSpan? RetryAfter { get; set; }
        public string? LimitType { get; set; }
        public Dictionary<string, string> RawHeaders { get; set; } = new();
    }

    /// <summary>
    /// 速率限制统计
    /// </summary>
    public class RateLimitStatistics
    {
        public ProviderType Provider { get; set; }
        public TimeSpan Period { get; set; }
        public int TotalRequests { get; set; }
        public int RateLimitedRequests { get; set; }
        public double RateLimitPercentage { get; set; }
        public TimeSpan AverageWaitTime { get; set; }
        public TimeSpan MaxWaitTime { get; set; }
        public Dictionary<string, int> LimitsByEndpoint { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 提供商适配器基类
    /// </summary>
    public abstract class ProviderAdapterBase : IOpenAICompatibleProvider
    {
        protected OpenAICompatibleProvider Configuration { get; }
        
        public ProviderType ProviderType => Configuration.ProviderType;
        public string ProviderName => Configuration.ProviderName;
        public virtual bool SupportsStreaming => Configuration.FeatureSupport.SupportsStreaming;

        protected ProviderAdapterBase(OpenAICompatibleProvider configuration)
        {
            Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        public abstract Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request);
        public abstract IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request);
        public abstract Task<List<ModelInfo>> GetAvailableModelsAsync();
        public abstract Task<ProviderHealthStatus> GetHealthStatusAsync();
        public abstract Task<PerformanceMetrics> GetPerformanceMetricsAsync();
        public abstract Task ConnectAsync();
        public abstract Task DisconnectAsync();
        
        public virtual void Dispose()
        {
            // 基类释放逻辑
        }
    }

    /// <summary>
    /// OpenAI提供商
    /// </summary>
    public class OpenAIProvider : ProviderAdapterBase
    {
        public OpenAIProvider(OpenAICompatibleProvider configuration) : base(configuration) { }

        public override async Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request)
        {
            // OpenAI特定实现
            await Task.CompletedTask;
            throw new NotImplementedException();
        }

        public override async IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request)
        {
            // OpenAI流式实现
            await Task.CompletedTask;
            yield break;
        }

        public override async Task<List<ModelInfo>> GetAvailableModelsAsync()
        {
            // 获取OpenAI模型列表
            await Task.CompletedTask;
            return new List<ModelInfo>();
        }

        public override async Task<ProviderHealthStatus> GetHealthStatusAsync()
        {
            await Task.CompletedTask;
            return ProviderHealthStatus.Healthy;
        }

        public override async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            await Task.CompletedTask;
            return new PerformanceMetrics();
        }

        public override async Task ConnectAsync()
        {
            await Task.CompletedTask;
        }

        public override async Task DisconnectAsync()
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Azure OpenAI提供商
    /// </summary>
    public class AzureOpenAIProvider : ProviderAdapterBase
    {
        public AzureOpenAIProvider(OpenAICompatibleProvider configuration) : base(configuration) { }

        public override async Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            throw new NotImplementedException();
        }

        public override async IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            yield break;
        }

        public override async Task<List<ModelInfo>> GetAvailableModelsAsync()
        {
            await Task.CompletedTask;
            return new List<ModelInfo>();
        }

        public override async Task<ProviderHealthStatus> GetHealthStatusAsync()
        {
            await Task.CompletedTask;
            return ProviderHealthStatus.Healthy;
        }

        public override async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            await Task.CompletedTask;
            return new PerformanceMetrics();
        }

        public override async Task ConnectAsync()
        {
            await Task.CompletedTask;
        }

        public override async Task DisconnectAsync()
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Anthropic提供商
    /// </summary>
    public class AnthropicProvider : ProviderAdapterBase
    {
        public AnthropicProvider(OpenAICompatibleProvider configuration) : base(configuration) { }

        public override async Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            throw new NotImplementedException();
        }

        public override async IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            yield break;
        }

        public override async Task<List<ModelInfo>> GetAvailableModelsAsync()
        {
            await Task.CompletedTask;
            return new List<ModelInfo>();
        }

        public override async Task<ProviderHealthStatus> GetHealthStatusAsync()
        {
            await Task.CompletedTask;
            return ProviderHealthStatus.Healthy;
        }

        public override async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            await Task.CompletedTask;
            return new PerformanceMetrics();
        }

        public override async Task ConnectAsync()
        {
            await Task.CompletedTask;
        }

        public override async Task DisconnectAsync()
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Google提供商
    /// </summary>
    public class GoogleProvider : ProviderAdapterBase
    {
        public GoogleProvider(OpenAICompatibleProvider configuration) : base(configuration) { }

        public override async Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            throw new NotImplementedException();
        }

        public override async IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            yield break;
        }

        public override async Task<List<ModelInfo>> GetAvailableModelsAsync()
        {
            await Task.CompletedTask;
            return new List<ModelInfo>();
        }

        public override async Task<ProviderHealthStatus> GetHealthStatusAsync()
        {
            await Task.CompletedTask;
            return ProviderHealthStatus.Healthy;
        }

        public override async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            await Task.CompletedTask;
            return new PerformanceMetrics();
        }

        public override async Task ConnectAsync()
        {
            await Task.CompletedTask;
        }

        public override async Task DisconnectAsync()
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Ollama提供商
    /// </summary>
    public class OllamaProvider : ProviderAdapterBase
    {
        public OllamaProvider(OpenAICompatibleProvider configuration) : base(configuration) { }

        public override async Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            throw new NotImplementedException();
        }

        public override async IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            yield break;
        }

        public override async Task<List<ModelInfo>> GetAvailableModelsAsync()
        {
            await Task.CompletedTask;
            return new List<ModelInfo>();
        }

        public override async Task<ProviderHealthStatus> GetHealthStatusAsync()
        {
            await Task.CompletedTask;
            return ProviderHealthStatus.Healthy;
        }

        public override async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            await Task.CompletedTask;
            return new PerformanceMetrics();
        }

        public override async Task ConnectAsync()
        {
            await Task.CompletedTask;
        }

        public override async Task DisconnectAsync()
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// 通用OpenAI提供商
    /// </summary>
    public class GenericOpenAIProvider : ProviderAdapterBase
    {
        public GenericOpenAIProvider(OpenAICompatibleProvider configuration) : base(configuration) { }

        public override async Task<ChatCompletionResponse> SendChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            throw new NotImplementedException();
        }

        public override async IAsyncEnumerable<StreamingChunk> SendStreamingChatCompletionAsync(ChatCompletionRequest request)
        {
            await Task.CompletedTask;
            yield break;
        }

        public override async Task<List<ModelInfo>> GetAvailableModelsAsync()
        {
            await Task.CompletedTask;
            return new List<ModelInfo>();
        }

        public override async Task<ProviderHealthStatus> GetHealthStatusAsync()
        {
            await Task.CompletedTask;
            return ProviderHealthStatus.Healthy;
        }

        public override async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            await Task.CompletedTask;
            return new PerformanceMetrics();
        }

        public override async Task ConnectAsync()
        {
            await Task.CompletedTask;
        }

        public override async Task DisconnectAsync()
        {
            await Task.CompletedTask;
        }
    }
}
