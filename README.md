# AITalk Framework

一个基于 .NET 9 的现代化 AI 对话框架，支持多轮调度、服务管理和 OpenAI 兼容 API 集成。

## 🎉 项目状态

✅ **构建成功** - 项目已成功编译，所有核心功能正常工作
✅ **示例运行** - 演示程序成功展示了框架的主要功能
✅ **架构完整** - 实现了完整的分层架构设计
✅ **任务调度** - 多轮任务调度和依赖管理正常工作
✅ **API管理** - OpenAI兼容API管理器已实现

### 最新测试结果
```
=== AITalk Framework Demo ===
Created service: OpenAI GPT-4 (ID: 87222ccf-fd6b-4b30-8e97-43fbf27652af)
Created conversation: AI Assistant Conversation (ID: c2c93514-f6c1-4519-9b80-9e3be810c99e)
Task execution simulation completed!
Total tasks completed: 3
All tasks successful: True
```

## 🏗️ 架构概述

AITalk 框架采用分层架构设计，主要包含以下核心组件：

### 核心组件

1. **AITalkServiceData** - 服务器数据管理
   - 管理第三方 AI 服务的配置信息
   - 包含服务器信息、认证、性能指标、使用统计等
   - 支持健康检查和负载均衡

2. **AITalkData** - 对话数据管理
   - 存储对话相关的所有数据
   - 包含消息历史、任务队列、上下文信息等
   - 支持对话状态管理和进度跟踪

3. **AITalk** - 对话逻辑控制
   - 负责具体的对话内容和多轮调度
   - 管理任务执行流程和依赖关系
   - 支持循环检测和中断策略

4. **AITalkService** - 服务管理
   - 管理第三方服务器的地址、方法、API Key 等
   - 提供服务发现、负载均衡、健康检查功能
   - 支持故障转移和性能监控

5. **OpenAIApiManager** - OpenAI 兼容 API 管理
   - 统一管理各种 OpenAI 兼容的第三方 API
   - 支持请求路由、响应标准化、兼容性检查
   - 包含速率限制、错误处理、流式响应等功能

## 🚀 主要特性

### 多轮任务调度
- **智能任务分解**：自动将复杂请求分解为多个子任务
- **依赖关系管理**：支持任务间的依赖关系和执行顺序
- **并行执行**：支持可并行任务的同时执行
- **循环检测**：自动检测和处理无限循环
- **动态调度**：根据执行结果动态生成新任务

### 服务管理
- **多服务支持**：同时管理多个 AI 服务提供商
- **负载均衡**：支持多种负载均衡策略
- **健康检查**：实时监控服务健康状态
- **故障转移**：自动切换到备用服务
- **性能监控**：收集和分析服务性能指标

### OpenAI 兼容性
- **统一接口**：为不同提供商提供统一的 OpenAI 格式接口
- **自动适配**：自动处理不同提供商的 API 差异
- **兼容性检查**：评估和报告 API 兼容性
- **请求转换**：自动转换请求格式以适配不同提供商
- **响应标准化**：将不同格式的响应标准化为 OpenAI 格式

### 数据管理
- **结构化存储**：完整的对话和服务数据结构
- **版本控制**：支持数据版本管理和回滚
- **缓存机制**：智能缓存提高性能
- **数据验证**：确保数据完整性和一致性
- **导出功能**：支持多种格式的数据导出

## 📦 项目结构

```
AITalk/
├── AITalk.Core/                    # 核心库
│   ├── Enums/                      # 枚举定义
│   │   ├── ServiceEnums.cs         # 服务相关枚举
│   │   ├── ConversationEnums.cs    # 对话相关枚举
│   │   └── OpenAIEnums.cs          # OpenAI 相关枚举
│   ├── Models/                     # 数据模型
│   │   ├── ServiceData/            # 服务数据模型
│   │   ├── ConversationData/       # 对话数据模型
│   │   ├── OpenAI/                 # OpenAI 相关模型
│   │   ├── AITalkServiceData.cs    # 服务数据主类
│   │   ├── AITalkData.cs           # 对话数据主类
│   │   └── ...                     # 其他模型类
│   └── Services/                   # 服务实现
│       ├── Interfaces/             # 服务接口
│       ├── AITalkService.cs        # 服务管理器
│       ├── AITalk.cs               # 对话控制器
│       └── OpenAIApiManager.cs     # OpenAI API 管理器
├── AITalk.Example/                 # 示例项目
│   └── Program.cs                  # 演示程序
└── README.md                       # 项目说明
```

## 🛠️ 快速开始

### 1. 安装依赖

```bash
dotnet restore
```

### 2. 运行示例

```bash
dotnet run --project AITalk.Example
```

### 3. 基本使用

```csharp
// 创建服务数据
var serviceData = new AITalkServiceData
{
    ServerInfo = new ServerInfo
    {
        ServerName = "OpenAI GPT-4",
        ServerType = ServiceType.OpenAI,
        BaseUrl = "https://api.openai.com/v1"
    },
    Authentication = new AuthenticationInfo
    {
        AuthType = AuthenticationType.ApiKey,
        ApiKey = "your-api-key"
    }
};

// 创建对话数据
var conversationData = new AITalkData
{
    ConversationInfo = new ConversationInfo
    {
        Title = "AI Assistant Conversation",
        ConversationType = ConversationType.MultiRound
    }
};

// 添加消息
var userMessage = new ConversationMessage
{
    Role = MessageRole.User,
    Content = "Hello, I need help with something."
};
conversationData.AddMessage(userMessage);

// 创建和执行任务
var task = new AITask
{
    TaskType = TaskType.InitialAnalysis,
    TaskName = "Analyze User Request",
    Description = "Analyze the user's request"
};

task.Start();
// ... 执行任务逻辑
task.Complete(result);
```

## 🔧 配置说明

### 服务配置

```csharp
var serviceData = new AITalkServiceData
{
    ServerInfo = new ServerInfo
    {
        ServerName = "Your Service Name",
        ServerType = ServiceType.OpenAI,
        BaseUrl = "https://api.example.com/v1",
        SupportedModels = new List<string> { "model1", "model2" }
    },
    Authentication = new AuthenticationInfo
    {
        AuthType = AuthenticationType.ApiKey,
        ApiKey = "your-encrypted-api-key"
    },
    Configuration = new ConfigurationSettings
    {
        ConnectionTimeout = TimeSpan.FromSeconds(30),
        MaxRetryAttempts = 3,
        EnableCompression = true
    }
};
```

### 对话配置

```csharp
var conversationConfig = new ConversationConfiguration
{
    MaxRounds = 50,
    MaxDuration = TimeSpan.FromHours(1),
    EnableAutoSave = true,
    EnableQualityAssessment = true
};
```

## 🎯 使用场景

1. **智能客服系统**：多轮对话处理复杂客户问题
2. **AI 助手应用**：任务分解和智能调度
3. **内容生成平台**：多步骤内容创作流程
4. **数据分析工具**：分步骤数据处理和分析
5. **教育辅导系统**：个性化学习路径规划

## 🔄 多轮调度示例

```csharp
// 第一轮：分析用户需求
var analysisTask = new AITask
{
    TaskType = TaskType.InitialAnalysis,
    TaskName = "Analyze Requirements",
    Round = 1
};

// 第二轮：收集信息（依赖第一轮结果）
var gatheringTask = new AITask
{
    TaskType = TaskType.InformationGathering,
    TaskName = "Gather Information",
    Round = 2,
    Dependencies = new List<Guid> { analysisTask.TaskId }
};

// 第三轮：生成最终结果
var synthesisTask = new AITask
{
    TaskType = TaskType.FinalSynthesis,
    TaskName = "Generate Final Result",
    Round = 3,
    Dependencies = new List<Guid> { gatheringTask.TaskId }
};
```

## 📊 监控和指标

框架提供丰富的监控和指标功能：

- **性能指标**：响应时间、吞吐量、错误率
- **使用统计**：Token 使用量、成本统计、请求分布
- **健康状态**：服务可用性、健康评分、告警信息
- **质量评估**：对话质量、任务成功率、用户满意度

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🆘 支持

如有问题或建议，请：

1. 查看文档和示例
2. 搜索已有的 Issues
3. 创建新的 Issue
4. 联系维护者

---

**AITalk Framework** - 让 AI 对话更智能、更可靠、更易用！
