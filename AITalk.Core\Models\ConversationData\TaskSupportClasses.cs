using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ConversationData
{
    /// <summary>
    /// 验证规则
    /// </summary>
    public class ValidationRule
    {
        public Guid RuleId { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Func<object?, bool> Validator { get; set; } = _ => true;
        public string ErrorMessage { get; set; } = string.Empty;
        public bool IsRequired { get; set; } = true;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 转换规则
    /// </summary>
    public class TransformationRule
    {
        public Guid RuleId { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Func<object?, object?> Transformer { get; set; } = input => input;
        public Type? InputType { get; set; }
        public Type? OutputType { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 任务配置
    /// </summary>
    public class TaskConfiguration
    {
        public TimeSpan? Timeout { get; set; }
        public int MaxRetries { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);
        public double RetryBackoffMultiplier { get; set; } = 2.0;
        public bool EnableLogging { get; set; } = true;
        public bool EnableMetrics { get; set; } = true;
        public bool EnableCaching { get; set; } = false;
        public TimeSpan? CacheExpiry { get; set; }
        public string CacheKey { get; set; } = string.Empty;
        public Dictionary<string, object> CustomSettings { get; set; } = new();
        public List<string> RequiredCapabilities { get; set; } = new();
        public Dictionary<string, object> ServicePreferences { get; set; } = new();

        public TaskConfiguration Clone()
        {
            return new TaskConfiguration
            {
                Timeout = Timeout,
                MaxRetries = MaxRetries,
                RetryDelay = RetryDelay,
                RetryBackoffMultiplier = RetryBackoffMultiplier,
                EnableLogging = EnableLogging,
                EnableMetrics = EnableMetrics,
                EnableCaching = EnableCaching,
                CacheExpiry = CacheExpiry,
                CacheKey = CacheKey,
                CustomSettings = new Dictionary<string, object>(CustomSettings),
                RequiredCapabilities = new List<string>(RequiredCapabilities),
                ServicePreferences = new Dictionary<string, object>(ServicePreferences)
            };
        }
    }

    /// <summary>
    /// 任务执行上下文
    /// </summary>
    public class TaskExecutionContext
    {
        public Guid ConversationId { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string SessionId { get; set; } = string.Empty;
        public Dictionary<string, object> Variables { get; set; } = new();
        public Dictionary<string, object> SharedData { get; set; } = new();
        public List<ConversationMessage> MessageHistory { get; set; } = new();
        public Dictionary<Guid, object> TaskOutputs { get; set; } = new();
        public Dictionary<string, object> ServiceContext { get; set; } = new();
        public DateTime ContextCreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        public void SetVariable(string key, object value)
        {
            Variables[key] = value;
            LastUpdated = DateTime.UtcNow;
        }

        public T? GetVariable<T>(string key)
        {
            if (Variables.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return default;
        }

        public void SetSharedData(string key, object value)
        {
            SharedData[key] = value;
            LastUpdated = DateTime.UtcNow;
        }

        public T? GetSharedData<T>(string key)
        {
            if (SharedData.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return default;
        }
    }

    /// <summary>
    /// 任务结果
    /// </summary>
    public class TaskResult
    {
        public bool IsSuccess { get; set; } = false;
        public object? Data { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan ProcessingTime { get; set; }
        public double ConfidenceScore { get; set; } = 0.0;
        public string ResultType { get; set; } = string.Empty;
        public Dictionary<string, double> Metrics { get; set; } = new();
    }

    /// <summary>
    /// 任务指标
    /// </summary>
    public class TaskMetrics
    {
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public int RetryCount { get; set; } = 0;
        public int ErrorCount { get; set; } = 0;
        public int WarningCount { get; set; } = 0;
        public long MemoryUsage { get; set; } = 0;
        public double CpuUsage { get; set; } = 0.0;
        public long NetworkBytes { get; set; } = 0;
        public int ApiCalls { get; set; } = 0;
        public decimal Cost { get; set; } = 0;
        public long TokensUsed { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> CustomMetrics { get; set; } = new();
    }

    /// <summary>
    /// 任务队列状态
    /// </summary>
    public class TaskQueueState
    {
        /// <summary>
        /// 待处理任务队列
        /// </summary>
        public Queue<AITask> PendingTasks { get; set; } = new();

        /// <summary>
        /// 执行中任务字典
        /// </summary>
        public Dictionary<Guid, AITask> ExecutingTasks { get; set; } = new();

        /// <summary>
        /// 已完成任务列表
        /// </summary>
        public List<AITask> CompletedTasks { get; set; } = new();

        /// <summary>
        /// 失败任务列表
        /// </summary>
        public List<AITask> FailedTasks { get; set; } = new();

        /// <summary>
        /// 取消任务列表
        /// </summary>
        public List<AITask> CancelledTasks { get; set; } = new();

        /// <summary>
        /// 最大并发任务数
        /// </summary>
        public int MaxConcurrentTasks { get; set; } = 5;

        /// <summary>
        /// 当前并发任务数
        /// </summary>
        public int CurrentConcurrentTasks => ExecutingTasks.Count;

        /// <summary>
        /// 任务执行顺序
        /// </summary>
        public TaskExecutionOrder TaskExecutionOrder { get; set; } = TaskExecutionOrder.FIFO;

        /// <summary>
        /// 任务重试策略
        /// </summary>
        public RetryPolicy TaskRetryPolicy { get; set; } = new();

        /// <summary>
        /// 任务超时时间
        /// </summary>
        public TimeSpan TaskTimeout { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// 队列容量
        /// </summary>
        public int QueueCapacity { get; set; } = 1000;

        /// <summary>
        /// 队列溢出策略
        /// </summary>
        public QueueOverflowStrategy QueueOverflowStrategy { get; set; } = QueueOverflowStrategy.Block;

        /// <summary>
        /// 任务调度策略
        /// </summary>
        public TaskSchedulingStrategy TaskSchedulingStrategy { get; set; } = TaskSchedulingStrategy.Immediate;

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 1;

        /// <summary>
        /// 批处理间隔
        /// </summary>
        public TimeSpan BatchInterval { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// 队列统计
        /// </summary>
        public QueueStatistics Statistics { get; set; } = new();

        /// <summary>
        /// 添加任务到队列
        /// </summary>
        public bool EnqueueTask(AITask task)
        {
            if (PendingTasks.Count >= QueueCapacity)
            {
                switch (QueueOverflowStrategy)
                {
                    case QueueOverflowStrategy.Drop:
                        return false;
                    case QueueOverflowStrategy.Block:
                        return false;
                    case QueueOverflowStrategy.Expand:
                        break; // 允许超过容量
                    case QueueOverflowStrategy.Reject:
                        throw new InvalidOperationException("Queue is full");
                }
            }

            PendingTasks.Enqueue(task);
            Statistics.TotalEnqueued++;
            Statistics.LastUpdated = DateTime.UtcNow;
            return true;
        }

        /// <summary>
        /// 从队列获取下一个任务
        /// </summary>
        public AITask? DequeueTask()
        {
            if (PendingTasks.Count == 0) return null;

            var task = PendingTasks.Dequeue();
            Statistics.TotalDequeued++;
            Statistics.LastUpdated = DateTime.UtcNow;
            return task;
        }

        /// <summary>
        /// 开始执行任务
        /// </summary>
        public bool StartExecutingTask(AITask task)
        {
            if (CurrentConcurrentTasks >= MaxConcurrentTasks)
                return false;

            ExecutingTasks[task.TaskId] = task;
            task.Start();
            Statistics.TotalStarted++;
            Statistics.LastUpdated = DateTime.UtcNow;
            return true;
        }

        /// <summary>
        /// 完成任务执行
        /// </summary>
        public void CompleteTask(AITask task)
        {
            if (ExecutingTasks.Remove(task.TaskId))
            {
                CompletedTasks.Add(task);
                Statistics.TotalCompleted++;
                Statistics.LastUpdated = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// 任务执行失败
        /// </summary>
        public void FailTask(AITask task)
        {
            if (ExecutingTasks.Remove(task.TaskId))
            {
                FailedTasks.Add(task);
                Statistics.TotalFailed++;
                Statistics.LastUpdated = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        public void CancelTask(AITask task)
        {
            if (ExecutingTasks.Remove(task.TaskId))
            {
                CancelledTasks.Add(task);
                Statistics.TotalCancelled++;
                Statistics.LastUpdated = DateTime.UtcNow;
            }
        }
    }

    /// <summary>
    /// 重试策略
    /// </summary>
    public class RetryPolicy
    {
        public RetryStrategy Strategy { get; set; } = RetryStrategy.Exponential;
        public int MaxAttempts { get; set; } = 3;
        public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);
        public double BackoffMultiplier { get; set; } = 2.0;
        public double Jitter { get; set; } = 0.1;
        public List<Type> RetryableExceptions { get; set; } = new();
        public List<Type> NonRetryableExceptions { get; set; } = new();
    }

    /// <summary>
    /// 队列统计
    /// </summary>
    public class QueueStatistics
    {
        public long TotalEnqueued { get; set; } = 0;
        public long TotalDequeued { get; set; } = 0;
        public long TotalStarted { get; set; } = 0;
        public long TotalCompleted { get; set; } = 0;
        public long TotalFailed { get; set; } = 0;
        public long TotalCancelled { get; set; } = 0;
        public TimeSpan AverageWaitTime { get; set; }
        public TimeSpan AverageExecutionTime { get; set; }
        public int PeakQueueLength { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public Dictionary<TaskType, long> TaskTypeStatistics { get; set; } = new();
    }
}
